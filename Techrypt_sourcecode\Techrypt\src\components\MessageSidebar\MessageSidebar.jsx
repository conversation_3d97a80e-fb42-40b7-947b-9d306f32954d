import React, { useState } from "react";
import "./MessageSidebar.css";
import { BsRobot } from "react-icons/bs";
import TechryptChatbot from "../TechryptChatbot/TechryptChatbot";

const MessageSidebar = () => {
  const [isChatbotOpen, setIsChatbotOpen] = useState(false);

  const toggleChatbot = () => {
    setIsChatbotOpen(!isChatbotOpen);
  };

  return (
    <>
      {/* Enhanced Chatbot Trigger Button with Green Theme */}
      <BsRobot
        title="Techrypt AI Assistant - ChatGPT-like Intelligence"
        className="fixed bottom-8 right-8 z-[999] text-white text-5xl bg-gradient-to-br from-green-500 to-green-600 rounded-full p-3 hover:from-green-600 hover:to-green-700 hover:scale-110 duration-300 transition-all cursor-pointer shadow-lg hover:shadow-xl"
        onClick={toggleChatbot}
      />

      {/* Enhanced TechryptChatbot with Smart Features */}
      {isChatbotOpen && (
        <TechryptChatbot
          isOpen={isChatbotOpen}
          onClose={() => setIsChatbotOpen(false)}
        />
      )}
    </>
  );
};

export default MessageSidebar;
