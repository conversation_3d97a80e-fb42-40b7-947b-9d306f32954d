#!/usr/bin/env python3
"""
Test script to verify the smart chatbot backend is working
"""

import requests
import json
import time

def test_backend():
    """Test the smart chatbot backend"""
    print("🧪 TESTING SMART CHATBOT BACKEND")
    print("=" * 50)
    
    base_url = "http://localhost:5000"
    
    # Test 1: Health check
    print("1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Health check passed")
            print(f"   Status: {health_data.get('status')}")
            print(f"   AI Backend: {health_data.get('ai_backend')}")
            print(f"   LLM Model: {health_data.get('llm_model')}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False
    
    # Test 2: Simple chat message
    print("\n2. Testing chat endpoint with 'hello'...")
    try:
        chat_data = {
            "message": "hello",
            "user_name": "",
            "user_context": {}
        }
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/chat", 
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            chat_response = response.json()
            print("✅ Chat test passed")
            print(f"   Response time: {response_time:.2f}s")
            print(f"   Bot response: {chat_response.get('response', '')[:100]}...")
            print(f"   Status: {chat_response.get('status')}")
            print(f"   Model: {chat_response.get('model')}")
        else:
            print(f"❌ Chat test failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Chat test error: {e}")
        return False
    
    # Test 3: Business query
    print("\n3. Testing business intelligence with 'I have a restaurant'...")
    try:
        chat_data = {
            "message": "I have a restaurant business",
            "user_name": "John",
            "user_context": {}
        }
        
        start_time = time.time()
        response = requests.post(
            f"{base_url}/chat", 
            json=chat_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        response_time = time.time() - start_time
        
        if response.status_code == 200:
            chat_response = response.json()
            print("✅ Business intelligence test passed")
            print(f"   Response time: {response_time:.2f}s")
            print(f"   Bot response: {chat_response.get('response', '')[:150]}...")
            print(f"   Business type detected: {chat_response.get('business_type', 'None')}")
        else:
            print(f"❌ Business test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Business test error: {e}")
        return False
    
    # Test 4: Context endpoint
    print("\n4. Testing context endpoint...")
    try:
        response = requests.get(f"{base_url}/context", timeout=5)
        if response.status_code == 200:
            context_data = response.json()
            print("✅ Context test passed")
            print(f"   AI Backend: {context_data.get('ai_backend')}")
            print(f"   Conversation length: {context_data.get('conversation_length')}")
        else:
            print(f"❌ Context test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Context test error: {e}")
        return False
    
    print("\n🎉 ALL TESTS PASSED!")
    print("✅ Smart chatbot backend is working correctly")
    print("✅ DialoGPT-medium model is responding")
    print("✅ Business intelligence is active")
    print("✅ Response times are good")
    return True

if __name__ == "__main__":
    test_backend()
