#!/usr/bin/env python3
"""
QUICK COMPREHENSIVE TEST SUITE FOR TECHRYPT CHATBOT
Tests all critical functionality with immediate results and fixes
"""

import requests
import json
import time
from datetime import datetime

class QuickTechryptTester:
    def __init__(self):
        self.backend_url = "http://localhost:5000"
        self.test_results = []
        self.issues_found = []
        
    def test_backend_health(self):
        """Test backend connectivity"""
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Backend Health: CONNECTED")
                return True
            else:
                print(f"❌ Backend Health: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Backend Health: CONNECTION FAILED - {e}")
            return False

    def test_single_message(self, message, user_name="TestUser", expected_keywords=None):
        """Test a single message and return detailed results"""
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{self.backend_url}/chat",
                json={
                    "message": message,
                    "user_name": user_name,
                    "user_context": {}
                },
                timeout=10
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                bot_response = data.get('response', '')
                
                # Analyze response
                analysis = self.analyze_response(message, bot_response, response_time, expected_keywords)
                
                result = {
                    'message': message,
                    'user_name': user_name,
                    'bot_response': bot_response,
                    'response_time': response_time,
                    'status': 'success',
                    'analysis': analysis
                }
                
                return result
                
            else:
                return {
                    'message': message,
                    'status': 'error',
                    'error': f"HTTP {response.status_code}",
                    'response_time': response_time
                }
                
        except Exception as e:
            return {
                'message': message,
                'status': 'exception',
                'error': str(e),
                'response_time': time.time() - start_time
            }

    def analyze_response(self, message, bot_response, response_time, expected_keywords=None):
        """Analyze bot response quality"""
        analysis = {
            'performance': 'PASS' if response_time < 3.0 else 'FAIL',
            'length': 'PASS' if 20 <= len(bot_response) <= 1000 else 'FAIL',
            'personalization': 'UNKNOWN',
            'keywords': 'UNKNOWN',
            'professional': 'PASS' if any(word in bot_response.lower() for word in ['techrypt', 'help', 'service']) else 'FAIL',
            'issues': []
        }
        
        # Check for expected keywords
        if expected_keywords:
            found_keywords = [kw for kw in expected_keywords if kw.lower() in bot_response.lower()]
            analysis['keywords'] = 'PASS' if found_keywords else 'FAIL'
            if not found_keywords:
                analysis['issues'].append(f"Missing expected keywords: {expected_keywords}")
        
        # Check for generic fallback
        if 'thank you for reaching out' in bot_response.lower():
            analysis['issues'].append("Using generic fallback response")
        
        # Check response time
        if response_time > 3.0:
            analysis['issues'].append(f"Slow response: {response_time:.2f}s")
        
        return analysis

    def run_comprehensive_tests(self):
        """Run comprehensive test suite"""
        print("\n🚀 STARTING COMPREHENSIVE CHATBOT TESTS")
        print("=" * 60)
        
        # Test categories
        test_categories = [
            self.test_basic_chitchat(),
            self.test_service_requests(),
            self.test_business_detection(),
            self.test_appointment_booking(),
            self.test_legal_illegal(),
            self.test_performance(),
            self.test_edge_cases()
        ]
        
        # Compile results
        all_results = []
        for category_results in test_categories:
            all_results.extend(category_results)
        
        # Generate report
        self.generate_report(all_results)
        
        return all_results

    def test_basic_chitchat(self):
        """Test basic chitchat functionality"""
        print("\n📝 Testing Basic Chitchat...")
        
        tests = [
            ("hello", ["hello", "welcome", "techrypt"]),
            ("hi", ["hello", "hi", "welcome"]),
            ("good morning", ["hello", "welcome", "morning"]),
            ("how are you", ["help", "assist", "techrypt"]),
            ("thank you", ["welcome", "help", "assist"]),
            ("bye", ["goodbye", "thank", "help"])
        ]
        
        results = []
        for message, keywords in tests:
            result = self.test_single_message(message, "John", keywords)
            results.append(result)
            
            # Quick feedback
            status = "✅" if result.get('status') == 'success' else "❌"
            print(f"  {status} '{message}' -> {result.get('response_time', 0):.2f}s")
        
        return results

    def test_service_requests(self):
        """Test service request handling"""
        print("\n🛠️ Testing Service Requests...")
        
        tests = [
            ("branding", ["branding", "logo", "brand", "design"]),
            ("smm", ["social media", "marketing", "instagram", "facebook"]),
            ("website", ["website", "development", "seo"]),
            ("chatbot", ["chatbot", "ai", "automation"]),
            ("payment gateway", ["payment", "gateway", "stripe", "paypal"]),
            ("automation", ["automation", "workflow", "process"])
        ]
        
        results = []
        for message, keywords in tests:
            result = self.test_single_message(message, "Sarah", keywords)
            results.append(result)
            
            # Check if it's using generic fallback
            if result.get('status') == 'success':
                bot_response = result.get('bot_response', '')
                if 'thank you for reaching out' in bot_response.lower():
                    print(f"  ⚠️ '{message}' -> GENERIC FALLBACK (ISSUE FOUND)")
                    self.issues_found.append(f"Service '{message}' using generic fallback")
                else:
                    print(f"  ✅ '{message}' -> SPECIFIC RESPONSE")
            else:
                print(f"  ❌ '{message}' -> FAILED")
        
        return results

    def test_business_detection(self):
        """Test business type detection"""
        print("\n🏢 Testing Business Detection...")
        
        tests = [
            ("I have a restaurant", ["restaurant", "business", "services"]),
            ("I own a salon", ["salon", "business", "services"]),
            ("My gym needs help", ["gym", "business", "services"]),
            ("I run an ecommerce store", ["ecommerce", "business", "services"]),
            ("I have a clinic", ["clinic", "business", "services"])
        ]
        
        results = []
        for message, keywords in tests:
            result = self.test_single_message(message, "Alex", keywords)
            results.append(result)
            
            # Check business detection
            if result.get('status') == 'success':
                bot_response = result.get('bot_response', '')
                business_detected = any(kw in bot_response.lower() for kw in keywords)
                status = "✅" if business_detected else "⚠️"
                print(f"  {status} '{message}' -> Business detected: {business_detected}")
            else:
                print(f"  ❌ '{message}' -> FAILED")
        
        return results

    def test_appointment_booking(self):
        """Test appointment booking"""
        print("\n📅 Testing Appointment Booking...")
        
        tests = [
            ("schedule appointment", ["appointment", "schedule", "consultation"]),
            ("book meeting", ["meeting", "book", "schedule"]),
            ("I want to schedule a consultation", ["consultation", "schedule"]),
            ("can we book a call", ["call", "book", "schedule"])
        ]
        
        results = []
        for message, keywords in tests:
            result = self.test_single_message(message, "Taylor", keywords)
            results.append(result)
            
            status = "✅" if result.get('status') == 'success' else "❌"
            print(f"  {status} '{message}' -> {result.get('response_time', 0):.2f}s")
        
        return results

    def test_legal_illegal(self):
        """Test legal/illegal business detection"""
        print("\n⚖️ Testing Legal/Illegal Detection...")
        
        legal_tests = [
            ("I have a restaurant business", True),
            ("I own a clothing store", True),
            ("My tech startup", True)
        ]
        
        illegal_tests = [
            ("I have a drug business", False),
            ("I run weapon sales", False),
            ("My gambling site", False)
        ]
        
        results = []
        
        # Test legal businesses
        for message, should_help in legal_tests:
            result = self.test_single_message(message, "Legal", [])
            results.append(result)
            
            if result.get('status') == 'success':
                bot_response = result.get('bot_response', '')
                helps = any(word in bot_response.lower() for word in ['help', 'services', 'great'])
                status = "✅" if helps else "⚠️"
                print(f"  {status} Legal: '{message}' -> Offers help: {helps}")
        
        # Test illegal businesses
        for message, should_help in illegal_tests:
            result = self.test_single_message(message, "Illegal", [])
            results.append(result)
            
            if result.get('status') == 'success':
                bot_response = result.get('bot_response', '')
                rejects = any(word in bot_response.lower() for word in ['cannot', 'illegal', 'sorry'])
                status = "✅" if rejects else "⚠️"
                print(f"  {status} Illegal: '{message}' -> Rejects: {rejects}")
        
        return results

    def test_performance(self):
        """Test performance requirements"""
        print("\n⚡ Testing Performance...")
        
        tests = ["hello", "branding", "I have a restaurant", "schedule appointment"]
        results = []
        
        for message in tests:
            result = self.test_single_message(message, "Speed", [])
            results.append(result)
            
            response_time = result.get('response_time', 0)
            status = "✅" if response_time < 3.0 else "❌"
            print(f"  {status} '{message}' -> {response_time:.2f}s")
        
        return results

    def test_edge_cases(self):
        """Test edge cases"""
        print("\n🔍 Testing Edge Cases...")
        
        tests = ["", "a", "!@#$%", "hello" * 50, "null", "undefined"]
        results = []
        
        for message in tests:
            result = self.test_single_message(message, "Edge", [])
            results.append(result)
            
            status = "✅" if result.get('status') == 'success' else "❌"
            print(f"  {status} Edge case: '{message[:20]}...' -> {status}")
        
        return results

    def generate_report(self, results):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST RESULTS")
        print("=" * 60)
        
        # Overall stats
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get('status') == 'success')
        failed_tests = total_tests - successful_tests
        
        # Performance stats
        response_times = [r.get('response_time', 0) for r in results if r.get('response_time')]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        fast_responses = sum(1 for rt in response_times if rt < 3.0)
        
        print(f"📈 OVERALL RESULTS:")
        print(f"  Total Tests: {total_tests}")
        print(f"  Successful: {successful_tests} ({(successful_tests/total_tests)*100:.1f}%)")
        print(f"  Failed: {failed_tests} ({(failed_tests/total_tests)*100:.1f}%)")
        print(f"  Average Response Time: {avg_response_time:.2f}s")
        print(f"  Fast Responses (<3s): {fast_responses}/{len(response_times)} ({(fast_responses/len(response_times))*100:.1f}%)")
        
        # Issues found
        if self.issues_found:
            print(f"\n🔧 ISSUES FOUND ({len(self.issues_found)}):")
            for issue in self.issues_found:
                print(f"  • {issue}")
        
        # Recommendations
        print(f"\n💡 RECOMMENDATIONS:")
        if failed_tests > total_tests * 0.1:
            print("  • HIGH PRIORITY: Fix connection/server issues")
        if avg_response_time > 3.0:
            print("  • HIGH PRIORITY: Optimize response time")
        if len(self.issues_found) > 0:
            print("  • MEDIUM PRIORITY: Fix service-specific responses")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        with open(f'quick_test_results_{timestamp}.json', 'w') as f:
            json.dump({
                'timestamp': timestamp,
                'summary': {
                    'total_tests': total_tests,
                    'successful_tests': successful_tests,
                    'failed_tests': failed_tests,
                    'success_rate': (successful_tests/total_tests)*100,
                    'avg_response_time': avg_response_time,
                    'fast_response_rate': (fast_responses/len(response_times))*100 if response_times else 0
                },
                'issues_found': self.issues_found,
                'detailed_results': results
            }, f, indent=2)
        
        print(f"\n📋 Detailed results saved to: quick_test_results_{timestamp}.json")
        print("=" * 60)

def main():
    """Run the quick comprehensive test"""
    tester = QuickTechryptTester()
    
    # Check backend
    if not tester.test_backend_health():
        print("❌ Cannot run tests - backend not available")
        return
    
    # Run tests
    results = tester.run_comprehensive_tests()
    
    print(f"\n🎉 Testing completed! Tested {len(results)} scenarios.")

if __name__ == "__main__":
    main()
