#!/usr/bin/env python3
"""
COMPREHENSIVE 10,000 TEST SUITE FOR TECHRYPT CHATBOT
Tests frontend-to-backend integration with detailed analytics
"""

import requests
import json
import time
import random
import asyncio
import aiohttp
import pandas as pd
from datetime import datetime
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TechryptChatbotTester:
    def __init__(self):
        self.backend_url = "http://localhost:5000"
        self.frontend_url = "http://localhost:5173"
        self.test_results = []
        self.performance_metrics = []
        self.accuracy_scores = {
            'basic_chitchat': {'correct': 0, 'total': 0},
            'service_requests': {'correct': 0, 'total': 0},
            'business_detection': {'correct': 0, 'total': 0},
            'appointment_booking': {'correct': 0, 'total': 0},
            'legal_illegal': {'correct': 0, 'total': 0},
            'complex_business': {'correct': 0, 'total': 0},
            'performance': {'under_3s': 0, 'total': 0},
            'personalization': {'correct': 0, 'total': 0}
        }
        
    def generate_test_scenarios(self):
        """Generate 10,000 diverse test scenarios"""
        scenarios = []
        
        # 1. Basic Chitchat (1000 tests)
        chitchat_patterns = [
            "hello", "hi", "hey", "good morning", "good afternoon", "good evening",
            "how are you", "what's up", "greetings", "salutations", "hola", "bonjour",
            "thank you", "thanks", "bye", "goodbye", "see you later", "have a good day",
            "nice to meet you", "pleasure talking", "great service", "awesome",
            "tell me about yourself", "who are you", "what do you do", "help me"
        ]
        
        for i in range(1000):
            base_msg = random.choice(chitchat_patterns)
            variations = [
                base_msg,
                base_msg.upper(),
                base_msg.capitalize(),
                f"{base_msg}!",
                f"{base_msg}?",
                f"um {base_msg}",
                f"{base_msg} please"
            ]
            scenarios.append({
                'category': 'basic_chitchat',
                'message': random.choice(variations),
                'user_name': random.choice(['John', 'Sarah', 'Mike', 'Lisa', 'David', '']),
                'expected_keywords': ['hello', 'welcome', 'techrypt', 'help', 'assist']
            })
        
        # 2. Service Requests (2000 tests)
        service_patterns = [
            # Website Development
            ("website", ["website", "development", "seo", "responsive"]),
            ("web development", ["website", "development", "seo"]),
            ("I need a website", ["website", "development", "consultation"]),
            ("web design", ["website", "development", "design"]),
            ("seo optimization", ["website", "seo", "optimization"]),
            
            # Social Media Marketing
            ("smm", ["social media", "marketing", "instagram", "facebook"]),
            ("social media marketing", ["social media", "marketing", "growth"]),
            ("instagram marketing", ["social media", "instagram", "marketing"]),
            ("facebook ads", ["social media", "facebook", "marketing"]),
            ("linkedin growth", ["social media", "linkedin", "marketing"]),
            
            # Branding
            ("branding", ["branding", "logo", "brand identity"]),
            ("logo design", ["branding", "logo", "design"]),
            ("brand identity", ["branding", "brand", "identity"]),
            ("graphic design", ["branding", "design", "graphics"]),
            
            # Chatbot Development
            ("chatbot", ["chatbot", "ai", "automation"]),
            ("ai chatbot", ["chatbot", "ai", "automation"]),
            ("customer service bot", ["chatbot", "customer service", "automation"]),
            
            # Automation
            ("automation", ["automation", "workflow", "process"]),
            ("business automation", ["automation", "business", "process"]),
            
            # Payment Gateway
            ("payment gateway", ["payment", "gateway", "stripe", "paypal"]),
            ("stripe integration", ["payment", "stripe", "integration"]),
            ("paypal setup", ["payment", "paypal", "integration"])
        ]
        
        for i in range(2000):
            service, keywords = random.choice(service_patterns)
            variations = [
                service,
                f"I need {service}",
                f"Can you help with {service}?",
                f"Tell me about {service}",
                f"What is {service}?",
                f"How much for {service}?",
                f"I want {service} for my business"
            ]
            scenarios.append({
                'category': 'service_requests',
                'message': random.choice(variations),
                'user_name': random.choice(['Alex', 'Emma', 'Chris', 'Taylor', '']),
                'expected_keywords': keywords
            })
        
        # 3. Business Detection (2000 tests)
        business_patterns = [
            ("I have a restaurant", "restaurant", ["restaurant", "business", "services"]),
            ("I own a salon", "salon", ["salon", "business", "services"]),
            ("My gym needs help", "gym", ["gym", "business", "services"]),
            ("I run an ecommerce store", "ecommerce", ["ecommerce", "business", "services"]),
            ("I have a clinic", "clinic", ["clinic", "business", "services"]),
            ("My agency", "agency", ["agency", "business", "services"]),
            ("I own a bakery", "bakery", ["bakery", "business", "services"]),
            ("My hotel business", "hotel", ["hotel", "business", "services"]),
            ("I have a law firm", "law firm", ["law firm", "business", "services"]),
            ("My consulting company", "consulting", ["consulting", "business", "services"])
        ]
        
        for i in range(2000):
            message, business_type, keywords = random.choice(business_patterns)
            variations = [
                message,
                message.replace("I have", "I own"),
                message.replace("I have", "I run"),
                f"{message} and need help",
                f"{message}, can you help?",
                f"Help me with {message.lower()}"
            ]
            scenarios.append({
                'category': 'business_detection',
                'message': random.choice(variations),
                'user_name': random.choice(['Jordan', 'Casey', 'Morgan', 'Riley', '']),
                'expected_keywords': keywords,
                'expected_business_type': business_type
            })
        
        # 4. Appointment Booking (1500 tests)
        appointment_patterns = [
            "schedule appointment", "book appointment", "schedule meeting", "book meeting",
            "schedule consultation", "book consultation", "schedule call", "book call",
            "I want to schedule", "I want to book", "I need to schedule", "I need to book",
            "can we schedule", "can we book", "let's schedule", "let's book",
            "appointment please", "meeting please", "consultation please",
            "when can we meet", "available times", "book a slot"
        ]
        
        for i in range(1500):
            base_msg = random.choice(appointment_patterns)
            variations = [
                base_msg,
                f"{base_msg} for next week",
                f"{base_msg} for tomorrow",
                f"I'd like to {base_msg}",
                f"Can I {base_msg}?",
                f"{base_msg} please"
            ]
            scenarios.append({
                'category': 'appointment_booking',
                'message': random.choice(variations),
                'user_name': random.choice(['Sam', 'Pat', 'Quinn', 'Blake', '']),
                'expected_keywords': ['schedule', 'appointment', 'consultation', 'meeting']
            })
        
        # 5. Legal/Illegal Business Detection (1000 tests)
        illegal_patterns = [
            ("drug business", True), ("weapon sales", True), ("gambling site", True),
            ("escort service", True), ("counterfeit goods", True), ("hacking services", True),
            ("fraud scheme", True), ("money laundering", True), ("pyramid scheme", True),
            ("illegal streaming", True)
        ]
        
        legal_patterns = [
            ("restaurant business", False), ("clothing store", False), ("tech startup", False),
            ("consulting firm", False), ("fitness center", False), ("beauty salon", False),
            ("online store", False), ("marketing agency", False), ("law firm", False),
            ("medical clinic", False)
        ]
        
        all_patterns = illegal_patterns + legal_patterns
        
        for i in range(1000):
            business, is_illegal = random.choice(all_patterns)
            variations = [
                f"I have a {business}",
                f"I run a {business}",
                f"My {business} needs help",
                f"Can you help with my {business}?",
                f"I want to start a {business}"
            ]
            scenarios.append({
                'category': 'legal_illegal',
                'message': random.choice(variations),
                'user_name': random.choice(['Terry', 'Jamie', 'Avery', 'Drew', '']),
                'expected_illegal': is_illegal,
                'expected_keywords': ['illegal', 'cannot assist'] if is_illegal else ['help', 'services']
            })
        
        # 6. Complex Business Scenarios (1500 tests)
        complex_patterns = [
            "I have a restaurant and need website, social media, and branding",
            "My ecommerce store needs payment gateway and chatbot integration",
            "I run a salon and want complete digital transformation",
            "My gym needs website, automation, and social media marketing",
            "I have a clinic and need HIPAA compliant solutions",
            "My agency needs white-label solutions for clients",
            "I run multiple businesses and need comprehensive packages",
            "My startup needs everything from branding to automation",
            "I have a franchise and need scalable solutions",
            "My enterprise needs custom development and integration"
        ]
        
        for i in range(1500):
            base_msg = random.choice(complex_patterns)
            variations = [
                base_msg,
                f"{base_msg}. What can you offer?",
                f"{base_msg}. How much would this cost?",
                f"{base_msg}. Can you help?",
                f"Help me with this: {base_msg}"
            ]
            scenarios.append({
                'category': 'complex_business',
                'message': random.choice(variations),
                'user_name': random.choice(['Phoenix', 'River', 'Sage', 'Sky', '']),
                'expected_keywords': ['services', 'comprehensive', 'consultation', 'help']
            })
        
        # 7. Performance & Edge Cases (1000 tests)
        edge_cases = [
            "", "   ", "a", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa",
            "!@#$%^&*()", "123456789", "hello" * 100, "test\n\n\ntest",
            "unicode: 你好 🚀 émojis", "SQL injection'; DROP TABLE--",
            "XSS <script>alert('test')</script>", "null", "undefined",
            "true", "false", "{}", "[]", "NaN", "Infinity"
        ]
        
        for i in range(1000):
            scenarios.append({
                'category': 'edge_cases',
                'message': random.choice(edge_cases),
                'user_name': random.choice(['Test', 'User', '', 'Edge']),
                'expected_keywords': ['help', 'techrypt', 'services']
            })
        
        return scenarios

    def test_backend_health(self):
        """Test backend health and connectivity"""
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ Backend Health: {data.get('status')}")
                logger.info(f"🤖 AI Model: {data.get('llm_model')}")
                return True
            else:
                logger.error(f"❌ Backend health check failed: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ Backend connection error: {e}")
            return False

    def test_single_scenario(self, scenario):
        """Test a single scenario and return results"""
        start_time = time.time()
        
        try:
            # Send request to backend
            response = requests.post(
                f"{self.backend_url}/chat",
                json={
                    "message": scenario['message'],
                    "user_name": scenario['user_name'],
                    "user_context": {}
                },
                timeout=10
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                bot_response = data.get('response', '')
                
                # Analyze response
                result = self.analyze_response(scenario, bot_response, response_time)
                result['status'] = 'success'
                result['response_time'] = response_time
                
            else:
                result = {
                    'status': 'error',
                    'error': f"HTTP {response.status_code}",
                    'response_time': response_time,
                    'category': scenario['category'],
                    'message': scenario['message'],
                    'accuracy': 0
                }
                
        except Exception as e:
            result = {
                'status': 'exception',
                'error': str(e),
                'response_time': time.time() - start_time,
                'category': scenario['category'],
                'message': scenario['message'],
                'accuracy': 0
            }
        
        return result

    def analyze_response(self, scenario, bot_response, response_time):
        """Analyze bot response for accuracy and correctness"""
        category = scenario['category']
        message = scenario['message']
        user_name = scenario['user_name']
        
        result = {
            'category': category,
            'message': message,
            'user_name': user_name,
            'bot_response': bot_response,
            'response_time': response_time,
            'accuracy': 0,
            'issues': []
        }
        
        # Performance check
        if response_time < 3.0:
            self.accuracy_scores['performance']['under_3s'] += 1
        self.accuracy_scores['performance']['total'] += 1
        
        # Personalization check
        if user_name and user_name in bot_response:
            self.accuracy_scores['personalization']['correct'] += 1
        self.accuracy_scores['personalization']['total'] += 1
        
        # Category-specific analysis
        if category == 'basic_chitchat':
            accuracy = self.analyze_chitchat(scenario, bot_response, result)
        elif category == 'service_requests':
            accuracy = self.analyze_service_request(scenario, bot_response, result)
        elif category == 'business_detection':
            accuracy = self.analyze_business_detection(scenario, bot_response, result)
        elif category == 'appointment_booking':
            accuracy = self.analyze_appointment_booking(scenario, bot_response, result)
        elif category == 'legal_illegal':
            accuracy = self.analyze_legal_illegal(scenario, bot_response, result)
        elif category == 'complex_business':
            accuracy = self.analyze_complex_business(scenario, bot_response, result)
        else:
            accuracy = 0.5  # Default for edge cases
        
        result['accuracy'] = accuracy
        return result

    def analyze_chitchat(self, scenario, bot_response, result):
        """Analyze basic chitchat responses"""
        expected_keywords = scenario.get('expected_keywords', [])
        response_lower = bot_response.lower()
        
        score = 0
        total_checks = 5
        
        # Check 1: Contains greeting/welcome
        if any(word in response_lower for word in ['hello', 'hi', 'welcome', 'greetings']):
            score += 1
        else:
            result['issues'].append("Missing greeting/welcome")
        
        # Check 2: Mentions Techrypt
        if 'techrypt' in response_lower:
            score += 1
        else:
            result['issues'].append("Missing Techrypt branding")
        
        # Check 3: Offers help
        if any(word in response_lower for word in ['help', 'assist', 'support']):
            score += 1
        else:
            result['issues'].append("Missing help offer")
        
        # Check 4: Professional tone
        if len(bot_response) > 20 and not any(word in response_lower for word in ['error', 'sorry', 'apologize']):
            score += 1
        else:
            result['issues'].append("Unprofessional or error response")
        
        # Check 5: Appropriate length
        if 50 <= len(bot_response) <= 500:
            score += 1
        else:
            result['issues'].append("Response length inappropriate")
        
        accuracy = score / total_checks
        
        if accuracy >= 0.7:
            self.accuracy_scores['basic_chitchat']['correct'] += 1
        self.accuracy_scores['basic_chitchat']['total'] += 1
        
        return accuracy

    def analyze_service_request(self, scenario, bot_response, result):
        """Analyze service request responses"""
        expected_keywords = scenario.get('expected_keywords', [])
        response_lower = bot_response.lower()

        score = 0
        total_checks = 6

        # Check 1: Contains expected service keywords
        keyword_found = any(keyword in response_lower for keyword in expected_keywords)
        if keyword_found:
            score += 1
        else:
            result['issues'].append(f"Missing expected keywords: {expected_keywords}")

        # Check 2: Provides service details
        if any(word in response_lower for word in ['create', 'develop', 'design', 'help', 'provide']):
            score += 1
        else:
            result['issues'].append("Missing service details")

        # Check 3: Includes call-to-action
        if any(phrase in response_lower for phrase in ['consultation', 'schedule', 'contact', 'discuss']):
            score += 1
        else:
            result['issues'].append("Missing call-to-action")

        # Check 4: Professional formatting
        if '•' in bot_response or '1.' in bot_response or '🌐' in bot_response:
            score += 1
        else:
            result['issues'].append("Missing professional formatting")

        # Check 5: Appropriate length for service description
        if 100 <= len(bot_response) <= 800:
            score += 1
        else:
            result['issues'].append("Service description length inappropriate")

        # Check 6: No generic fallback
        if not any(phrase in response_lower for phrase in ['thank you for reaching out', 'could you tell me more']):
            score += 1
        else:
            result['issues'].append("Using generic fallback instead of specific service response")

        accuracy = score / total_checks

        if accuracy >= 0.7:
            self.accuracy_scores['service_requests']['correct'] += 1
        self.accuracy_scores['service_requests']['total'] += 1

        return accuracy

    def analyze_business_detection(self, scenario, bot_response, result):
        """Analyze business type detection"""
        expected_business_type = scenario.get('expected_business_type', '')
        response_lower = bot_response.lower()

        score = 0
        total_checks = 5

        # Check 1: Detects business type
        if expected_business_type.lower() in response_lower:
            score += 1
        else:
            result['issues'].append(f"Failed to detect business type: {expected_business_type}")

        # Check 2: Provides relevant services
        if any(word in response_lower for word in ['services', 'help', 'grow', 'business']):
            score += 1
        else:
            result['issues'].append("Missing relevant service offerings")

        # Check 3: Lists specific services
        service_count = sum(1 for service in ['website', 'social media', 'branding', 'chatbot', 'automation', 'payment']
                          if service in response_lower)
        if service_count >= 3:
            score += 1
        else:
            result['issues'].append("Insufficient service listings")

        # Check 4: Asks follow-up question
        if '?' in bot_response:
            score += 1
        else:
            result['issues'].append("Missing follow-up question")

        # Check 5: Professional business tone
        if any(phrase in response_lower for phrase in ['great', 'excellent', 'perfect']) and 'business' in response_lower:
            score += 1
        else:
            result['issues'].append("Missing professional business acknowledgment")

        accuracy = score / total_checks

        if accuracy >= 0.7:
            self.accuracy_scores['business_detection']['correct'] += 1
        self.accuracy_scores['business_detection']['total'] += 1

        return accuracy

    def analyze_appointment_booking(self, scenario, bot_response, result):
        """Analyze appointment booking responses"""
        response_lower = bot_response.lower()

        score = 0
        total_checks = 4

        # Check 1: Acknowledges appointment request
        if any(word in response_lower for word in ['appointment', 'schedule', 'meeting', 'consultation']):
            score += 1
        else:
            result['issues'].append("Failed to acknowledge appointment request")

        # Check 2: Provides booking guidance
        if any(phrase in response_lower for phrase in ['form', 'book', 'schedule', 'available']):
            score += 1
        else:
            result['issues'].append("Missing booking guidance")

        # Check 3: Professional response
        if any(word in response_lower for word in ['happy', 'glad', 'pleased']) or 'help' in response_lower:
            score += 1
        else:
            result['issues'].append("Missing professional acknowledgment")

        # Check 4: Appropriate length
        if 30 <= len(bot_response) <= 300:
            score += 1
        else:
            result['issues'].append("Appointment response length inappropriate")

        accuracy = score / total_checks

        if accuracy >= 0.7:
            self.accuracy_scores['appointment_booking']['correct'] += 1
        self.accuracy_scores['appointment_booking']['total'] += 1

        return accuracy

    def analyze_legal_illegal(self, scenario, bot_response, result):
        """Analyze legal/illegal business detection"""
        expected_illegal = scenario.get('expected_illegal', False)
        response_lower = bot_response.lower()

        score = 0
        total_checks = 3

        if expected_illegal:
            # Should reject illegal business
            if any(phrase in response_lower for phrase in ['cannot assist', 'illegal', 'sorry', 'unable to help']):
                score += 2
            else:
                result['issues'].append("Failed to reject illegal business")

            # Should not offer services
            if not any(word in response_lower for word in ['website', 'marketing', 'branding']):
                score += 1
            else:
                result['issues'].append("Incorrectly offered services for illegal business")
        else:
            # Should accept legal business
            if any(word in response_lower for word in ['help', 'services', 'great', 'excellent']):
                score += 2
            else:
                result['issues'].append("Failed to accept legal business")

            # Should offer services
            if any(word in response_lower for word in ['website', 'marketing', 'branding', 'services']):
                score += 1
            else:
                result['issues'].append("Failed to offer services for legal business")

        accuracy = score / total_checks

        if accuracy >= 0.7:
            self.accuracy_scores['legal_illegal']['correct'] += 1
        self.accuracy_scores['legal_illegal']['total'] += 1

        return accuracy

    def analyze_complex_business(self, scenario, bot_response, result):
        """Analyze complex business scenario responses"""
        response_lower = bot_response.lower()

        score = 0
        total_checks = 5

        # Check 1: Acknowledges complexity
        if any(word in response_lower for word in ['comprehensive', 'complete', 'multiple', 'all']):
            score += 1
        else:
            result['issues'].append("Failed to acknowledge complexity")

        # Check 2: Lists multiple services
        service_count = sum(1 for service in ['website', 'social media', 'branding', 'chatbot', 'automation', 'payment']
                          if service in response_lower)
        if service_count >= 4:
            score += 1
        else:
            result['issues'].append("Insufficient service coverage for complex request")

        # Check 3: Suggests consultation
        if any(word in response_lower for word in ['consultation', 'discuss', 'meeting', 'call']):
            score += 1
        else:
            result['issues'].append("Missing consultation suggestion")

        # Check 4: Professional tone
        if any(word in response_lower for word in ['excellent', 'great', 'perfect', 'comprehensive']):
            score += 1
        else:
            result['issues'].append("Missing professional acknowledgment")

        # Check 5: Appropriate length for complex response
        if 150 <= len(bot_response) <= 1000:
            score += 1
        else:
            result['issues'].append("Complex response length inappropriate")

        accuracy = score / total_checks

        if accuracy >= 0.7:
            self.accuracy_scores['complex_business']['correct'] += 1
        self.accuracy_scores['complex_business']['total'] += 1

        return accuracy

    def run_batch_tests(self, scenarios, batch_size=50):
        """Run tests in batches for better performance"""
        total_scenarios = len(scenarios)
        batches = [scenarios[i:i + batch_size] for i in range(0, total_scenarios, batch_size)]

        logger.info(f"🚀 Running {total_scenarios} tests in {len(batches)} batches of {batch_size}")

        all_results = []
        failed_requests = 0
        max_failures = 100  # Stop if too many failures

        for batch_num, batch in enumerate(batches, 1):
            logger.info(f"📊 Processing batch {batch_num}/{len(batches)}")

            # Use ThreadPoolExecutor with reduced workers for stability
            with ThreadPoolExecutor(max_workers=5) as executor:
                future_to_scenario = {executor.submit(self.test_single_scenario, scenario): scenario
                                    for scenario in batch}

                batch_results = []
                for future in as_completed(future_to_scenario):
                    try:
                        result = future.result()
                        batch_results.append(result)

                        # Track failures
                        if result.get('status') != 'success':
                            failed_requests += 1

                    except Exception as e:
                        logger.error(f"Test failed with exception: {e}")
                        failed_requests += 1
                        batch_results.append({
                            'status': 'failed',
                            'error': str(e),
                            'accuracy': 0,
                            'category': 'unknown',
                            'message': 'test_failed',
                            'response_time': 0
                        })

            all_results.extend(batch_results)

            # Progress update
            completed = len(all_results)
            progress = (completed / total_scenarios) * 100
            success_rate = ((completed - failed_requests) / completed) * 100 if completed > 0 else 0

            logger.info(f"✅ Progress: {completed}/{total_scenarios} ({progress:.1f}%) - Success: {success_rate:.1f}%")

            # Stop if too many failures
            if failed_requests > max_failures:
                logger.warning(f"⚠️ Stopping tests due to high failure rate ({failed_requests} failures)")
                break

            # Brief pause between batches
            time.sleep(0.5)

        return all_results

    def generate_comprehensive_report(self, results):
        """Generate detailed test report with analytics"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_tests': len(results),
            'summary': {},
            'category_breakdown': {},
            'performance_metrics': {},
            'accuracy_scores': self.accuracy_scores,
            'issues_found': [],
            'recommendations': []
        }

        # Overall summary
        successful_tests = sum(1 for r in results if r.get('status') == 'success')
        failed_tests = len(results) - successful_tests
        avg_accuracy = sum(r.get('accuracy', 0) for r in results) / len(results) if results else 0
        avg_response_time = sum(r.get('response_time', 0) for r in results) / len(results) if results else 0

        report['summary'] = {
            'success_rate': (successful_tests / len(results)) * 100,
            'failure_rate': (failed_tests / len(results)) * 100,
            'average_accuracy': avg_accuracy * 100,
            'average_response_time': avg_response_time,
            'tests_under_3s': self.accuracy_scores['performance']['under_3s'],
            'performance_rate': (self.accuracy_scores['performance']['under_3s'] /
                               self.accuracy_scores['performance']['total']) * 100
        }

        # Category breakdown
        categories = ['basic_chitchat', 'service_requests', 'business_detection',
                     'appointment_booking', 'legal_illegal', 'complex_business']

        for category in categories:
            category_results = [r for r in results if r.get('category') == category]
            if category_results:
                category_accuracy = sum(r.get('accuracy', 0) for r in category_results) / len(category_results)
                category_response_time = sum(r.get('response_time', 0) for r in category_results) / len(category_results)

                report['category_breakdown'][category] = {
                    'total_tests': len(category_results),
                    'accuracy': category_accuracy * 100,
                    'avg_response_time': category_response_time,
                    'success_rate': (sum(1 for r in category_results if r.get('status') == 'success') /
                                   len(category_results)) * 100
                }

        # Collect issues
        all_issues = []
        for result in results:
            if 'issues' in result:
                all_issues.extend(result['issues'])

        # Count issue frequency
        issue_counts = {}
        for issue in all_issues:
            issue_counts[issue] = issue_counts.get(issue, 0) + 1

        report['issues_found'] = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)

        # Generate recommendations
        report['recommendations'] = self.generate_recommendations(report)

        return report

    def generate_recommendations(self, report):
        """Generate improvement recommendations based on test results"""
        recommendations = []

        # Performance recommendations
        if report['summary']['performance_rate'] < 95:
            recommendations.append({
                'priority': 'HIGH',
                'category': 'Performance',
                'issue': f"Only {report['summary']['performance_rate']:.1f}% of responses under 3 seconds",
                'solution': 'Optimize LLM inference, add response caching, reduce model complexity'
            })

        # Accuracy recommendations
        for category, data in report['category_breakdown'].items():
            if data['accuracy'] < 80:
                recommendations.append({
                    'priority': 'HIGH',
                    'category': 'Accuracy',
                    'issue': f"{category} accuracy only {data['accuracy']:.1f}%",
                    'solution': f'Improve {category} training data and response patterns'
                })

        # Common issues
        for issue, count in report['issues_found'][:5]:  # Top 5 issues
            if count > len(report) * 0.1:  # If issue affects >10% of tests
                recommendations.append({
                    'priority': 'MEDIUM',
                    'category': 'Quality',
                    'issue': f"Common issue: {issue} ({count} occurrences)",
                    'solution': 'Review and improve response templates'
                })

        return recommendations

    def save_results(self, results, report):
        """Save test results and report to files"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save detailed results
        results_df = pd.DataFrame(results)
        results_df.to_csv(f'test_results_{timestamp}.csv', index=False)

        # Save report
        with open(f'test_report_{timestamp}.json', 'w') as f:
            json.dump(report, f, indent=2)

        logger.info(f"📊 Results saved to test_results_{timestamp}.csv")
        logger.info(f"📋 Report saved to test_report_{timestamp}.json")

def main():
    """Main test execution function"""
    tester = TechryptChatbotTester()

    # Check backend health
    if not tester.test_backend_health():
        logger.error("❌ Backend not available. Please start the backend server first.")
        return

    # Generate test scenarios
    logger.info("🎯 Generating 10,000 test scenarios...")
    scenarios = tester.generate_test_scenarios()
    logger.info(f"✅ Generated {len(scenarios)} test scenarios")

    # Run tests in batches (reduced for faster completion)
    logger.info("🚀 Starting comprehensive testing...")
    # Use first 1000 scenarios for faster testing
    test_scenarios = scenarios[:1000]
    logger.info(f"📊 Testing {len(test_scenarios)} scenarios for comprehensive analysis")
    results = tester.run_batch_tests(test_scenarios, batch_size=25)

    # Generate report
    logger.info("📊 Generating comprehensive report...")
    report = tester.generate_comprehensive_report(results)

    # Save results
    tester.save_results(results, report)

    # Print summary
    print("\n" + "="*80)
    print("🎉 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("="*80)
    print(f"📊 Total Tests: {report['total_tests']}")
    print(f"✅ Success Rate: {report['summary']['success_rate']:.1f}%")
    print(f"🎯 Average Accuracy: {report['summary']['average_accuracy']:.1f}%")
    print(f"⚡ Average Response Time: {report['summary']['average_response_time']:.2f}s")
    print(f"🚀 Performance Rate (<3s): {report['summary']['performance_rate']:.1f}%")

    print("\n📈 CATEGORY BREAKDOWN:")
    for category, data in report['category_breakdown'].items():
        print(f"  {category}: {data['accuracy']:.1f}% accuracy, {data['avg_response_time']:.2f}s avg")

    print("\n🔧 TOP ISSUES:")
    for issue, count in report['issues_found'][:5]:
        print(f"  • {issue}: {count} occurrences")

    print("\n💡 RECOMMENDATIONS:")
    for rec in report['recommendations'][:5]:
        print(f"  [{rec['priority']}] {rec['category']}: {rec['issue']}")
        print(f"      Solution: {rec['solution']}")

    print("="*80)

if __name__ == "__main__":
    main()
