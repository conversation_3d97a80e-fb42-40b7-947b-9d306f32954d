#!/usr/bin/env python3
"""
🤖 SMART LLM CHATBOT - ChatGPT-like Intelligence for Techrypt
Fast, intelligent, business-focused AI assistant with <3 second responses
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import sys
import os
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the enhanced AI backend
try:
    from Techrypt_sourcecode.Techrypt.src.ai_backend import get_ai_response, techrypt_ai
    from Techrypt_sourcecode.Techrypt.src.mongodb_backend import get_mongodb
    AI_BACKEND_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ AI backend import error: {e}")
    AI_BACKEND_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app, origins=['http://localhost:5173', 'http://localhost:3000'])

# Global variables for performance tracking
response_times = []
total_requests = 0
successful_requests = 0

@app.route('/health', methods=['GET'])
def health_check():
    """Enhanced health check with AI status"""
    global total_requests, successful_requests
    
    avg_response_time = sum(response_times[-100:]) / len(response_times[-100:]) if response_times else 0
    success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 0
    
    status = {
        "status": "healthy",
        "service": "Smart LLM Chatbot",
        "version": "2.0.0",
        "ai_backend": "available" if AI_BACKEND_AVAILABLE else "unavailable",
        "llm_model": "microsoft/DialoGPT-medium" if AI_BACKEND_AVAILABLE else "none",
        "performance": {
            "avg_response_time": f"{avg_response_time:.2f}s",
            "total_requests": total_requests,
            "success_rate": f"{success_rate:.1f}%"
        },
        "features": [
            "ChatGPT-like intelligence",
            "Business context awareness", 
            "Smart service routing",
            "Appointment booking intelligence",
            "MongoDB data persistence",
            "Response caching for speed"
        ]
    }
    
    return jsonify(status)

@app.route('/chat', methods=['POST'])
def smart_chat():
    """Smart chat endpoint with ChatGPT-like intelligence"""
    global total_requests, successful_requests, response_times
    
    start_time = time.time()
    total_requests += 1
    
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        user_message = data.get('message', '').strip()
        user_name = data.get('user_name', '').strip()
        user_context = data.get('user_context', {})

        # Handle empty messages gracefully
        if not user_message:
            user_message = "hello"

        logger.info(f"📨 Smart chat request: '{user_message}' from user: '{user_name}'")

        # Generate intelligent response
        if AI_BACKEND_AVAILABLE:
            # Update AI context with user information
            if user_context:
                techrypt_ai.user_context.update(user_context)
            
            # Generate response using enhanced AI
            ai_response = get_ai_response(user_message, user_name)
            
            # Analyze response for intelligent triggers
            response_analysis = analyze_response_for_triggers(user_message, ai_response)
            
            response_data = {
                'response': ai_response,
                'status': 'success',
                'timestamp': datetime.now().isoformat(),
                'model': 'DialoGPT-medium',
                'mode': 'intelligent_hybrid',
                'show_contact_form': response_analysis.get('show_contact_form', False),
                'show_appointment_form': response_analysis.get('show_appointment_form', False),
                'business_type': response_analysis.get('business_type'),
                'services_mentioned': response_analysis.get('services_mentioned', []),
                'user_context': techrypt_ai.user_context
            }
        else:
            # Fallback intelligent response
            ai_response = generate_intelligent_fallback(user_message, user_name)
            response_analysis = analyze_response_for_triggers(user_message, ai_response)
            
            response_data = {
                'response': ai_response,
                'status': 'fallback',
                'timestamp': datetime.now().isoformat(),
                'model': 'fallback_intelligent',
                'mode': 'fallback',
                'show_contact_form': response_analysis.get('show_contact_form', False),
                'show_appointment_form': response_analysis.get('show_appointment_form', False)
            }

        # Track performance
        response_time = time.time() - start_time
        response_times.append(response_time)
        successful_requests += 1
        
        logger.info(f"✅ Response generated in {response_time:.2f}s")
        
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"❌ Smart chat error: {e}")
        response_time = time.time() - start_time
        response_times.append(response_time)
        
        return jsonify({
            'response': 'I apologize for the technical difficulty. How can Techrypt help your business today?',
            'status': 'error',
            'timestamp': datetime.now().isoformat(),
            'error': str(e)
        }), 500

def analyze_response_for_triggers(user_message, ai_response):
    """Analyze response for intelligent form triggers"""
    analysis = {
        'show_contact_form': False,
        'show_appointment_form': False,
        'business_type': None,
        'services_mentioned': []
    }
    
    msg_lower = user_message.lower()
    response_lower = ai_response.lower()
    
    # Detect contact form triggers
    contact_triggers = [
        'email', 'contact', 'phone', 'call me', 'reach out', 'get in touch',
        'provide your email', 'need your email', 'email to get started'
    ]
    
    if any(trigger in msg_lower or trigger in response_lower for trigger in contact_triggers):
        analysis['show_contact_form'] = True
    
    # Detect appointment form triggers
    appointment_triggers = [
        'schedule', 'book', 'appointment', 'consultation', 'meeting',
        'call', 'demo', 'yes please', 'sure', 'excellent', 'perfect'
    ]
    
    if any(trigger in msg_lower for trigger in appointment_triggers):
        analysis['show_appointment_form'] = True
    
    # Detect business type
    business_patterns = [
        'restaurant', 'cafe', 'bakery', 'salon', 'gym', 'clinic',
        'store', 'shop', 'boutique', 'agency', 'firm', 'company'
    ]
    
    for pattern in business_patterns:
        if pattern in msg_lower:
            analysis['business_type'] = pattern
            break
    
    # Detect services mentioned
    service_keywords = {
        'website': ['website', 'web', 'site', 'online'],
        'social_media': ['social', 'instagram', 'facebook', 'marketing'],
        'branding': ['logo', 'brand', 'design', 'branding'],
        'chatbot': ['chatbot', 'bot', 'automation'],
        'payment': ['payment', 'gateway', 'checkout'],
        'automation': ['automation', 'workflow', 'process']
    }
    
    for service, keywords in service_keywords.items():
        if any(keyword in msg_lower for keyword in keywords):
            analysis['services_mentioned'].append(service)
    
    return analysis

def generate_intelligent_fallback(user_message, user_name):
    """Generate intelligent fallback response when AI backend is unavailable"""
    msg_lower = user_message.lower()
    name_part = f", {user_name}" if user_name else ""
    
    # Business-specific responses
    if any(word in msg_lower for word in ['business', 'company', 'store', 'shop']):
        return f"""Great{name_part}! I'd love to learn more about your business. Techrypt specializes in helping businesses grow through:

🌐 Website Development - Professional online presence
📱 Social Media Marketing - Engage your customers  
🎨 Branding Services - Create memorable identity
🤖 Chatbot Development - Automate customer service
⚡ Automation Packages - Streamline operations
💳 Payment Gateway Integration - Secure transactions

What type of business do you have?"""
    
    # Service inquiries
    if any(word in msg_lower for word in ['service', 'help', 'what do you do']):
        return f"""Hello{name_part}! I'm here to help you grow your business with Techrypt's digital services:

1. Website Development
2. Social Media Marketing
3. Branding Services
4. Chatbot Development
5. Automation Packages
6. Payment Gateway Integration

Which service interests you most, or would you like to schedule a consultation?"""
    
    # Appointment requests
    if any(word in msg_lower for word in ['appointment', 'schedule', 'book', 'meeting']):
        return f"""Perfect{name_part}! I'd be happy to help you schedule a consultation with our Techrypt team. We offer 15-20 minute consultations to discuss your specific business needs and how we can help you grow.

Please provide your email address to get started with booking your appointment!"""
    
    # Greeting responses
    if any(word in msg_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
        return f"""Hello{name_part}! Welcome to Techrypt.io! I'm your intelligent assistant here to help you grow your business with our digital services.

What type of business do you have, and how can I help you today?"""
    
    # Default intelligent response
    return f"""Thank you for your message{name_part}! I'm here to help you grow your business with Techrypt's comprehensive digital services. Could you tell me more about what you're looking for, or would you like to schedule a consultation to discuss your needs?"""

@app.route('/context', methods=['GET'])
def get_context():
    """Get current conversation context"""
    try:
        if AI_BACKEND_AVAILABLE:
            return jsonify({
                'user_context': techrypt_ai.user_context,
                'conversation_length': len(techrypt_ai.conversation_history),
                'status': 'success',
                'ai_backend': 'available'
            })
        else:
            return jsonify({
                'user_context': {},
                'conversation_length': 0,
                'status': 'fallback',
                'ai_backend': 'unavailable'
            })
    except Exception as e:
        logger.error(f"Error getting context: {e}")
        return jsonify({'error': 'Failed to get context'}), 500

@app.route('/reset', methods=['POST'])
def reset_context():
    """Reset conversation context"""
    try:
        if AI_BACKEND_AVAILABLE:
            techrypt_ai.conversation_history = []
            techrypt_ai.user_context = {
                'name': '', 'email': '', 'phone': '', 'business_type': '',
                'interests': [], 'previous_topics': [], 'services_discussed': [],
                'session_id': '', 'conversation_count': 0, 'user_id': None,
                'last_interaction': None, 'appointment_intent': False,
                'contact_form_shown': False, 'business_needs_identified': []
            }
            return jsonify({'message': 'Context reset successfully', 'status': 'success'})
        else:
            return jsonify({'message': 'Context reset (fallback mode)', 'status': 'fallback'})
    except Exception as e:
        logger.error(f"Error resetting context: {e}")
        return jsonify({'error': 'Failed to reset context'}), 500

def main():
    """Main function to start the smart chatbot server"""
    print("🤖 SMART LLM CHATBOT SERVER")
    print("=" * 60)
    print("🎯 ChatGPT-like Intelligence for Techrypt")
    print("⚡ Fast responses (<3 seconds)")
    print("🧠 Business context awareness")
    print("📊 Smart service routing")
    print("=" * 60)
    
    if AI_BACKEND_AVAILABLE:
        print("✅ AI Backend: Available")
        print("🤖 LLM Model: microsoft/DialoGPT-medium")
        print("💾 MongoDB: Integrated")
        print("📈 CSV Training: 10,042+ lines")
    else:
        print("⚠️ AI Backend: Fallback mode")
        print("🔄 Intelligent fallback responses active")
    
    print("\n🚀 Starting Smart Chatbot Server...")
    print("📡 Server: http://localhost:5000")
    print("🔗 Health: http://localhost:5000/health")
    print("💬 Chat: POST http://localhost:5000/chat")
    print("📊 Context: GET http://localhost:5000/context")
    print("🔄 Reset: POST http://localhost:5000/reset")
    print("=" * 60)
    
    # Start server
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,
        threaded=True
    )

if __name__ == "__main__":
    main()
