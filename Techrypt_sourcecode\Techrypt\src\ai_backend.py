#!/usr/bin/env python3
"""
Advanced AI Backend for Techrypt Chatbot
Fully trained on CSV data with LLM integration for AI automation agency
Implements all requirements from user memories for intelligent business responses
"""

import json
import re
import csv
import os
import random
import time
from datetime import datetime
from typing import List, Dict, Any, Tuple
import logging
from difflib import SequenceMatcher

# Configure logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import production modules
try:
    from mongodb_backend import get_mongodb, TechryptMongoDB
    from intelligent_service_detector import detect_user_needs
    PRODUCTION_MODULES_AVAILABLE = True
    logger.info("✅ Production modules loaded: MongoDB + Intelligent Service Detection")
except ImportError as e:
    PRODUCTION_MODULES_AVAILABLE = False
    logger.warning(f"⚠️ Production modules not available: {e}")

# Advanced ML and NLP libraries
try:
    from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
    import torch
    HF_AVAILABLE = True
    logger.info("Hugging Face Transformers available")
except ImportError:
    HF_AVAILABLE = False
    logger.warning("Hugging Face Transformers not available, using advanced CSV-based intelligence")

try:
    import pandas as pd
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    ADVANCED_ML_AVAILABLE = True
    logger.info("Advanced ML libraries available for enhanced CSV training")
except ImportError:
    ADVANCED_ML_AVAILABLE = False
    logger.warning("Advanced ML libraries not available, using basic CSV processing")

class TechryptAI:
    def __init__(self):
        """CHATGPT-LIKE INTELLIGENT AI INITIALIZATION"""
        self.conversation_history = []
        self.user_context = {
            'name': '',
            'email': '',
            'phone': '',
            'business_type': '',
            'interests': [],
            'previous_topics': [],
            'services_discussed': [],
            'session_id': '',
            'conversation_count': 0,
            'user_id': None,  # MongoDB user ID
            'last_interaction': None,
            'appointment_intent': False,
            'contact_form_shown': False,
            'business_needs_identified': []
        }

        # Store user's business context for intelligent follow-up
        self.user_business_context = ""
        self.conversation_context = ""

        # Performance optimization caches
        self.response_cache = {}
        self.business_intelligence_cache = {}
        self.service_mapping_cache = {}

        # Conversation intelligence tracking
        self.intent_history = []
        self.response_times = []
        self.user_satisfaction_indicators = []

        # PRODUCTION: Initialize MongoDB connection
        self.mongodb = None
        if PRODUCTION_MODULES_AVAILABLE:
            try:
                self.mongodb = get_mongodb()
                logger.info("✅ MongoDB connection established")
            except Exception as e:
                logger.error(f"❌ MongoDB connection failed: {e}")

        # Load and fully train on CSV data
        self.training_data = self.load_csv_data()
        self.trained_model = self.train_on_csv_data()

        # Load business context from information.txt
        self.business_context = self.load_business_context()

        # Initialize DialoGPT-medium for enhanced responses
        self.llm_pipeline = self.initialize_llm()

        # PRODUCTION: Enhanced service detection
        self.service_keywords = [
            'website', 'web', 'site', 'online', 'ecommerce', 'shopify', 'daraz',
            'social media', 'smm', 'instagram', 'facebook', 'linkedin', 'marketing',
            'branding', 'logo', 'design', 'brand', 'graphics',
            'chatbot', 'bot', 'ai', 'automation', 'customer service',
            'payment', 'gateway', 'stripe', 'paypal', 'transactions',
            'appointment', 'schedule', 'booking', 'consultation', 'meeting'
        ]

    def initialize_llm(self):
        """WINDOWS-COMPATIBLE LLM initialization with enhanced error handling"""
        try:
            from transformers import pipeline
            import gc
            import platform
            import threading
            import time

            # WINDOWS FIX: Force garbage collection before loading
            gc.collect()

            # Check and clean disk space before loading models
            self.check_and_clean_disk_space()

            # PRODUCTION FIX: Enable online mode with aggressive download settings
            if 'TRANSFORMERS_OFFLINE' in os.environ:
                del os.environ['TRANSFORMERS_OFFLINE']
            if 'HF_HUB_OFFLINE' in os.environ:
                del os.environ['HF_HUB_OFFLINE']

            # Set aggressive download timeout and retry settings
            os.environ['HF_HUB_DOWNLOAD_TIMEOUT'] = '1800'  # 30 minutes
            os.environ['HF_HUB_DOWNLOAD_RETRIES'] = '10'    # 10 retries
            os.environ['CURL_CA_BUNDLE'] = ''               # Clear SSL issues
            os.environ['REQUESTS_CA_BUNDLE'] = ''           # Clear SSL issues
            logger.info("🌐 Using online mode with aggressive download settings")

            # OPTIMIZED: Only DialoGPT-medium for best performance
            models_to_try = [
                "microsoft/DialoGPT-medium",  # OPTIMAL: Best model for business chatbots (345M parameters, ~1.2GB)
            ]

            for model_name in models_to_try:
                try:
                    logger.info(f"🔄 Loading LLM model for Windows: {model_name}")

                    # WINDOWS FIX: Use threading timeout instead of signal (Windows compatible)
                    model_loaded = [None]
                    error_occurred = [None]

                    def load_model():
                        try:
                            # WINDOWS FIX: Minimal configuration for maximum compatibility
                            chatbot = pipeline(
                                "text-generation",
                                model=model_name,
                                tokenizer=model_name,
                                device=-1,  # Force CPU for stability
                                framework="pt",  # Explicitly use PyTorch
                                return_full_text=True,
                                clean_up_tokenization_spaces=True,
                                trust_remote_code=False  # Security
                            )
                            model_loaded[0] = chatbot
                        except Exception as e:
                            error_occurred[0] = e

                    # Start model loading in thread with timeout
                    loading_thread = threading.Thread(target=load_model)
                    loading_thread.daemon = True
                    loading_thread.start()
                    loading_thread.join(timeout=60)  # 60-second timeout

                    if model_loaded[0] is not None:
                        chatbot = model_loaded[0]

                        # WINDOWS FIX: Test the model with a simple query
                        try:
                            test_response = chatbot(
                                "Hello",
                                max_length=30,
                                num_return_sequences=1,
                                do_sample=False,  # Deterministic for testing
                                pad_token_id=50256,
                                truncation=True
                            )

                            if test_response and len(test_response) > 0:
                                logger.info(f"✅ LLM MODEL LOADED SUCCESSFULLY: {model_name}")
                                logger.info(f"✅ Model test passed - ready for complex scenarios")
                                return chatbot
                            else:
                                logger.warning(f"Model test failed for {model_name}")
                                continue

                        except Exception as test_error:
                            logger.error(f"Model test error for {model_name}: {test_error}")
                            continue

                    elif error_occurred[0] is not None:
                        logger.error(f"Model loading error for {model_name}: {error_occurred[0]}")
                        continue
                    else:
                        logger.error(f"Model loading timeout for {model_name}")
                        continue

                except Exception as e:
                    logger.warning(f"Failed to initialize {model_name}: {e}")
                    continue

            logger.warning("⚠️ All LLM models failed to load - using enhanced CSV mode")
            logger.info("✅ System will use CSV responses and business intelligence")
            return None

        except Exception as e:
            logger.error(f"Critical error in LLM initialization: {e}")
            logger.info("✅ Falling back to enhanced CSV-based responses")
            return None

    def load_csv_data(self):
        """Load training data from large CSV file (10,040 lines)"""
        training_data = []
        # Use the large data.csv file from the parent directory (10,040 lines)
        csv_path = os.path.join(os.path.dirname(__file__), '..', 'data.csv')

        try:
            with open(csv_path, 'r', encoding='utf-8') as file:
                # Read the file line by line to handle malformed CSV
                lines = file.readlines()

                # Skip header (first line)
                if lines:
                    header_line = lines[0].strip()
                    logger.info(f"CSV header line: {header_line}")

                    # Process data lines
                    for line_num, line in enumerate(lines[1:], 1):
                        line = line.strip()
                        if line:
                            # Handle the unusual CSV format where entire line is quoted
                            if line.startswith('"') and line.endswith('"'):
                                # Remove outer quotes
                                line = line[1:-1]

                            # Now split by comma, but handle double quotes in responses
                            fields = []
                            current_field = ""
                            in_double_quotes = False
                            i = 0

                            while i < len(line):
                                char = line[i]

                                if char == '"' and i + 1 < len(line) and line[i + 1] == '"':
                                    # Double quote - this is the start/end of bot response
                                    if not in_double_quotes:
                                        in_double_quotes = True
                                        i += 2  # Skip both quotes
                                        continue
                                    else:
                                        in_double_quotes = False
                                        i += 2  # Skip both quotes
                                        continue
                                elif char == ',' and not in_double_quotes:
                                    # Field separator
                                    fields.append(current_field)
                                    current_field = ""
                                    i += 1
                                else:
                                    current_field += char
                                    i += 1

                            # Add the last field
                            fields.append(current_field)

                            if len(fields) >= 4:
                                training_data.append({
                                    'question': fields[0].lower(),  # user_input
                                    'answer': fields[3],            # bot_response
                                    'category': fields[1],          # intent
                                    'keywords': fields[2].lower().split() if fields[2] else []  # entities
                                })
                            elif len(fields) >= 2:  # Fallback for incomplete rows
                                training_data.append({
                                    'question': fields[0].lower(),
                                    'answer': fields[1] if len(fields) > 1 else "",
                                    'category': fields[2] if len(fields) > 2 else "general",
                                    'keywords': fields[3].lower().split() if len(fields) > 3 and fields[3] else []
                                })
            logger.info(f"Loaded {len(training_data)} training examples from large CSV dataset")
        except FileNotFoundError:
            logger.warning("Large CSV training data file not found, using fallback responses")
        except Exception as e:
            logger.error(f"Error loading CSV data: {e}")

        return training_data

    def load_business_context(self):
        """Load business context from informatiomj.txt"""
        business_context = ""
        info_path = os.path.join(os.path.dirname(__file__), 'informatiomj.txt')

        try:
            with open(info_path, 'r', encoding='utf-8') as file:
                business_context = file.read()
            logger.info(f"Loaded business context from informatiomj.txt ({len(business_context)} characters)")
        except FileNotFoundError:
            logger.warning("informatiomj.txt file not found")
        except Exception as e:
            logger.error(f"Error loading business context: {e}")

        return business_context

    def train_on_csv_data(self):
        """Fully train on CSV data using advanced ML techniques"""
        if not self.training_data:
            logger.warning("No training data available for model training")
            return None

        try:
            if ADVANCED_ML_AVAILABLE:
                # Use TF-IDF vectorization for semantic understanding
                questions = [item['question'] for item in self.training_data]
                answers = [item['answer'] for item in self.training_data]

                # Create TF-IDF vectorizer
                self.vectorizer = TfidfVectorizer(
                    ngram_range=(1, 3),  # Use 1-3 word combinations
                    stop_words='english',
                    max_features=5000,
                    lowercase=True
                )

                # Fit vectorizer on all questions
                self.question_vectors = self.vectorizer.fit_transform(questions)

                logger.info(f"Successfully trained TF-IDF model on {len(questions)} examples")
                return {
                    'vectorizer': self.vectorizer,
                    'question_vectors': self.question_vectors,
                    'answers': answers,
                    'training_size': len(questions)
                }
            else:
                # Fallback to enhanced keyword matching
                logger.info("Using enhanced keyword-based training")
                return self.create_keyword_model()

        except Exception as e:
            logger.error(f"Error training on CSV data: {e}")
            return None

    def create_keyword_model(self):
        """Create enhanced keyword-based model"""
        keyword_map = {}
        for item in self.training_data:
            for keyword in item['keywords']:
                if keyword not in keyword_map:
                    keyword_map[keyword] = []
                keyword_map[keyword].append(item)

        return {
            'keyword_map': keyword_map,
            'training_size': len(self.training_data)
        }



    def find_best_match(self, user_message: str):
        """Find the best matching response using trained model"""
        if not self.training_data:
            return None

        try:
            # Use advanced ML model if available
            if self.trained_model and ADVANCED_ML_AVAILABLE and 'vectorizer' in self.trained_model:
                return self.find_semantic_match(user_message)
            else:
                # Use enhanced keyword matching
                return self.find_enhanced_keyword_match(user_message)

        except Exception as e:
            logger.error(f"Error finding best match: {e}")
            return self.find_basic_match(user_message)

    def find_semantic_match(self, user_message: str):
        """Find match using TF-IDF semantic similarity"""
        try:
            # Transform user message to vector
            user_vector = self.trained_model['vectorizer'].transform([user_message.lower()])

            # Calculate cosine similarity with all training questions
            similarities = cosine_similarity(user_vector, self.trained_model['question_vectors']).flatten()

            # Find best match
            best_idx = similarities.argmax()
            best_score = similarities[best_idx]

            # Return match if similarity is above threshold
            if best_score > 0.3:  # Semantic similarity threshold
                return {
                    'answer': self.trained_model['answers'][best_idx],
                    'score': best_score,
                    'method': 'semantic',
                    'question': self.training_data[best_idx]['question'],
                    'category': self.training_data[best_idx]['category']
                }

            return None

        except Exception as e:
            logger.error(f"Semantic matching error: {e}")
            return None

    def find_enhanced_keyword_match(self, user_message: str):
        """Simple keyword matching"""
        user_msg_lower = user_message.lower()
        best_match = None
        best_score = 0

        for data in self.training_data:
            # Check exact question match first
            if data['question'] == user_msg_lower:
                return {
                    'answer': data['answer'],
                    'score': 1.0,
                    'method': 'exact',
                    'question': data['question'],
                    'category': data['category']
                }

            # Simple keyword matching
            keyword_matches = sum(1 for keyword in data['keywords'] if keyword in user_msg_lower)

            if keyword_matches > 0:
                # Simple similarity score
                similarity = SequenceMatcher(None, user_msg_lower, data['question']).ratio()
                score = (keyword_matches * 0.6) + (similarity * 0.4)

                if score > best_score and score > 0.3:
                    best_score = score
                    best_match = {
                        'answer': data['answer'],
                        'score': score,
                        'method': 'keyword',
                        'question': data['question'],
                        'category': data['category']
                    }

        return best_match

    def find_basic_match(self, user_message: str):
        """Basic fallback matching"""
        user_msg_lower = user_message.lower()

        for data in self.training_data:
            if data['question'] == user_msg_lower:
                return {
                    'answer': data['answer'],
                    'score': 1.0,
                    'method': 'basic',
                    'question': data['question'],
                    'category': data['category']
                }

            # Simple keyword check
            if any(keyword in user_msg_lower for keyword in data['keywords']):
                return {
                    'answer': data['answer'],
                    'score': 0.7,
                    'method': 'basic',
                    'question': data['question'],
                    'category': data['category']
                }

        return None

    def personalize_csv_response(self, csv_match: Dict, user_message: str) -> str:
        """Simple personalization of CSV responses"""
        response = csv_match['answer']
        user_name = self.user_context['name']

        # Remove asterisks from responses
        response = response.replace('*', '')

        # Simple personalization with name if provided
        if user_name and user_name.strip() and user_name not in response:
            if response.startswith('Great!'):
                response = f"Great, {user_name}!" + response[6:]
            elif response.startswith('Hello!'):
                response = f"Hello, {user_name}!" + response[6:]

        return response

    def get_simple_llm_response(self, message: str, business_type: str = 'general') -> str:
        """Simple, fast LLM response for immediate use"""
        if not self.llm_pipeline:
            return ""

        try:
            # Create focused business prompt
            if business_type == 'electronics':
                prompt = f"User with electronics business said: '{message}'. Respond about Techrypt digital marketing services for electronics stores."
            else:
                prompt = f"User with {business_type} business said: '{message}'. Respond about Techrypt digital marketing services."

            # Conservative generation to avoid garbage
            result = self.llm_pipeline(
                prompt,
                max_new_tokens=30,  # Reduced to avoid garbage
                do_sample=False,    # Deterministic to avoid random text
                temperature=0.1,    # Very low temperature
                pad_token_id=50256,
                truncation=True
            )

            if result and len(result) > 0:
                response = result[0]['generated_text'].replace(prompt, "").strip()

                # Filter out garbage responses
                garbage_indicators = [
                    "thanks, i'll edit",
                    "thanks for your help",
                    "very much",
                    len(response) < 10,
                    not any(word in response.lower() for word in ['techrypt', 'service', 'help', 'marketing', 'website'])
                ]

                if any(garbage_indicators):
                    return ""  # Return empty to fall back to CSV

                return response[:150] if response else ""

        except Exception as e:
            logger.error(f"Simple LLM error: {e}")

        return ""

    def save_to_excel(self, appointment_data: Dict):
        """Save appointment data to Excel file"""
        try:
            excel_file = 'Techrypt_Appointment_Scheduling.xlsx'

            # Create new appointment record
            new_appointment = {
                'Appointment_ID': f"APT{datetime.now().strftime('%Y%m%d%H%M%S')}",
                'Date_Created': datetime.now().strftime('%Y-%m-%d'),
                'Client_Name': appointment_data.get('name', ''),
                'Client_Email': appointment_data.get('email', ''),
                'Client_Phone': appointment_data.get('phone', ''),
                'Business_Type': appointment_data.get('business_type', ''),
                'Services_Requested': ', '.join(appointment_data.get('services', [])),
                'Preferred_Date': appointment_data.get('date', ''),
                'Preferred_Time': appointment_data.get('time', ''),
                'Duration_Minutes': 20,
                'Status': 'Scheduled',
                'Assigned_Consultant': 'TBD',
                'Meeting_Type': 'Video Call',
                'Meeting_Link': '',
                'Notes': appointment_data.get('notes', ''),
                'Lead_Source': 'Chatbot',
                'Priority': 'Medium',
                'Follow_Up_Required': 'Yes',
                'Estimated_Project_Value': '$3000',
                'Conversion_Status': 'Pending'
            }

            # Try to append to existing file or create new one
            try:
                df = pd.read_excel(excel_file, sheet_name='Appointments')
                df = pd.concat([df, pd.DataFrame([new_appointment])], ignore_index=True)
            except FileNotFoundError:
                df = pd.DataFrame([new_appointment])

            # Save to Excel
            with pd.ExcelWriter(excel_file, engine='openpyxl', mode='w') as writer:
                df.to_excel(writer, sheet_name='Appointments', index=False)

            logger.info(f"✅ Appointment saved to Excel: {new_appointment['Appointment_ID']}")
            return True

        except Exception as e:
            logger.error(f"❌ Excel save error: {e}")
            return False

    def add_to_history(self, user_message: str, bot_response: str):
        """PRODUCTION-SAFE conversation history with memory management"""
        try:
            self.conversation_history.append({
                'timestamp': datetime.now().isoformat(),
                'user': user_message[:500],  # Limit message length
                'bot': bot_response[:1000]   # Limit response length
            })

            # PRODUCTION FIX: Aggressive memory management for stability
            if len(self.conversation_history) > 10:  # Reduced from 20
                self.conversation_history = self.conversation_history[-10:]

            # PRODUCTION FIX: Force garbage collection every 10 conversations
            if len(self.conversation_history) % 10 == 0:
                import gc
                gc.collect()

        except Exception as e:
            logger.error(f"Error adding to history: {e}")
            # Reset history if corrupted
            self.conversation_history = []

    def update_user_context(self, user_name: str, message: str):
        """PRODUCTION: Update user context with MongoDB integration"""
        try:
            # Update name if provided
            if user_name and user_name.strip():
                self.user_context['name'] = user_name.strip()

                # Create/update user in MongoDB
                if self.mongodb and not self.user_context.get('user_id'):
                    try:
                        user_id = self.mongodb.create_user(
                            name=user_name,
                            business_type=self.user_context.get('business_type')
                        )
                        self.user_context['user_id'] = user_id
                        logger.info(f"✅ User created/updated in MongoDB: {user_name}")
                    except Exception as e:
                        logger.error(f"MongoDB user creation error: {e}")

            # Detect and update business type
            if PRODUCTION_MODULES_AVAILABLE:
                try:
                    service_analysis = detect_user_needs(message, self.conversation_history)
                    if service_analysis.get('business_type') and service_analysis['business_type'] != 'general':
                        self.user_context['business_type'] = service_analysis['business_type']
                        self.user_business_context = service_analysis['business_type']
                except Exception as e:
                    logger.error(f"Business type detection error: {e}")

        except Exception as e:
            logger.error(f"User context update error: {e}")

    def handle_appointment_request(self, message: str, service_analysis: Dict) -> str:
        """PRODUCTION: Handle appointment scheduling with MongoDB"""
        try:
            user_name = self.user_context.get('name', '')

            # Check if user is registered
            if not self.user_context.get('user_id'):
                return f"""I'd be happy to schedule a consultation for you{', ' + user_name if user_name else ''}!

To book your appointment, I'll need a few details:
📧 Your email address
📱 Your phone number (optional)
🏢 Your business type

Once I have these details, I can schedule your consultation with our Techrypt team. We offer 15-20 minute consultations to discuss your specific needs and how we can help grow your business.

Please provide your email to get started!"""

            # If user is registered, proceed with scheduling
            return f"""Perfect{', ' + user_name if user_name else ''}! I can help you schedule a consultation with our Techrypt team.

Our available consultation slots:
🕘 Monday-Friday: 9:00 AM - 6:00 PM
⏱️ Duration: 15-20 minutes
💼 Focus: Your business growth needs

To complete your booking, please let me know:
📅 Your preferred date
🕐 Your preferred time
🎯 Specific services you're interested in

Would you like to schedule for this week or next week?"""

        except Exception as e:
            logger.error(f"Appointment handling error: {e}")
            return "I'd be happy to help you schedule a consultation! Please let me know your preferred date and time, and I'll check our availability."

    def generate_production_response(self, message: str, service_analysis: Dict = None) -> str:
        """PRODUCTION: Generate intelligent response using all available systems"""
        try:
            # PRIORITY 1: Try LLM with service analysis context
            if self.llm_pipeline and service_analysis:
                # Create intelligent business response
                business_type = service_analysis.get('business_type', 'general')
                detected_services = service_analysis.get('detected_services', [])

                # INTELLIGENT ELECTRONICS SHOWROOM RESPONSES
                if business_type == 'electronics' or 'electronics' in message.lower() or 'showroom' in message.lower():
                    if 'marketing' in message.lower() or 'social media' in message.lower():
                        return f"Perfect! For your electronics showroom, I can help you with Social Media Marketing. We specialize in:\n\n📱 **Electronics Store Social Media:**\n- Product showcases and demonstrations\n- Customer reviews and testimonials\n- Seasonal promotions and sales campaigns\n- Tech tips and product comparisons\n- Local customer engagement\n\nOur Social Media Marketing team knows how to showcase electronics effectively. Would you like to schedule a consultation to discuss your marketing strategy?"

                    elif 'website' in message.lower():
                        return f"Excellent! For your electronics showroom, I can create a professional website with:\n\n🌐 **Electronics Store Website:**\n- Product catalog with detailed specifications\n- Online ordering and inventory management\n- Customer reviews and ratings\n- Technical support and warranty information\n- Mobile-responsive design for all devices\n\nWould you like to discuss your website requirements?"

                    else:
                        # General electronics showroom response
                        return f"Great! For your electronics showroom, Techrypt can help you with:\n\n🔌 **Complete Digital Solutions:**\n1. 🌐 Website Development - Product catalogs and online sales\n2. 📱 Social Media Marketing - Showcase your latest electronics\n3. 🎨 Branding Services - Professional electronics store branding\n4. 🤖 Chatbot Development - Customer support and product inquiries\n5. ⚡ Automation Packages - Inventory and customer management\n6. 💳 Payment Gateway Integration - Secure online transactions\n\nWhich service interests you most, or would you like to schedule a consultation to discuss your specific needs?"

                # Try simple LLM response
                simple_response = self.get_simple_llm_response(message, business_type)
                if simple_response and len(simple_response.strip()) > 20:
                    logger.info("✅ Using simple LLM response")
                    return simple_response

            # PRIORITY 2: Try enhanced LLM
            if self.llm_pipeline:
                llm_response = self.get_enhanced_llm_response_with_context(message)
                if llm_response and len(llm_response.strip()) > 30:
                    logger.info("✅ Using enhanced LLM")
                    return llm_response

            # PRIORITY 3: Use intelligent CSV with service analysis
            if service_analysis:
                csv_response = self.find_best_match(message)
                if csv_response:
                    logger.info("✅ Using CSV with service context")
                    return csv_response

            # PRIORITY 4: Use enhanced CSV
            csv_match = self.find_best_match(message)
            if csv_match:
                response = self.personalize_csv_response(csv_match, message)
                response = self.enhance_response_with_branding(response)
                logger.info("✅ Using enhanced CSV response")
                return response

            # FALLBACK: Intelligent fallback based on service analysis
            return self.get_contextual_response(message)

        except Exception as e:
            logger.error(f"Production response generation error: {e}")
            return self.get_contextual_response(message)

    def generate_intelligent_response(self, user_message: str, user_name: str = '') -> str:
        """CHATGPT-LIKE INTELLIGENT RESPONSE GENERATION - Fast, Smart, Context-Aware"""
        start_time = time.time()

        try:
            # Update user context intelligently
            self._update_user_context(user_message, user_name)

            # Check response cache for performance
            cache_key = self._generate_cache_key(user_message)
            if cache_key in self.response_cache:
                cached_response = self.response_cache[cache_key]
                logger.info("⚡ Using cached response")
                return self._personalize_response(cached_response, user_message)

            # Detect user intent and business needs
            intent_analysis = self._analyze_user_intent(user_message)
            business_intelligence = self._get_business_intelligence(user_message)

            # Generate response using hybrid LLM + CSV approach
            response = self._generate_hybrid_response(user_message, intent_analysis, business_intelligence)

            # Cache response for performance
            if response and len(response) > 30:
                self.response_cache[cache_key] = response

            # Track conversation context
            self._update_conversation_context(user_message, response)

            # Save to MongoDB if available
            self._save_conversation_to_db(user_message, response)

            return response

        except Exception as e:
            logger.error(f"Error in intelligent response generation: {e}")
            return "I apologize for the technical difficulty. How can Techrypt help your business today?"

        finally:
            # Track response time for optimization
            response_time = time.time() - start_time
            self.response_times.append(response_time)
            logger.info(f"⚡ Response generated in {response_time:.2f} seconds")

    def _update_user_context(self, user_message: str, user_name: str = ''):
        """Update user context intelligently"""
        try:
            # Update name if provided
            if user_name and user_name.strip():
                self.user_context['name'] = user_name.strip()

            # Extract email from message
            import re
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, user_message)
            if emails:
                self.user_context['email'] = emails[0]

            # Extract phone from message
            phone_pattern = r'(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
            phones = re.findall(phone_pattern, user_message)
            if phones:
                self.user_context['phone'] = ''.join(phones[0]) if isinstance(phones[0], tuple) else phones[0]

            # Detect business type
            business_type = self.detect_business_type(user_message)
            if business_type:
                self.user_context['business_type'] = business_type

            # Track appointment intent
            appointment_keywords = ['appointment', 'schedule', 'book', 'meeting', 'consultation', 'call', 'demo']
            if any(keyword in user_message.lower() for keyword in appointment_keywords):
                self.user_context['appointment_intent'] = True

        except Exception as e:
            logger.error(f"Error updating user context: {e}")

    def _generate_cache_key(self, user_message: str) -> str:
        """Generate cache key for response caching"""
        import hashlib
        # Create hash from message + business context for intelligent caching
        context_str = f"{user_message.lower()}_{self.user_context.get('business_type', '')}"
        return hashlib.md5(context_str.encode()).hexdigest()[:16]

    def _personalize_response(self, response: str, user_message: str) -> str:
        """Personalize cached response with user context"""
        user_name = self.user_context.get('name', '')
        if user_name and ', ' not in response and user_name not in response:
            # Add personalization if not already present
            if response.startswith('Perfect!') or response.startswith('Great!') or response.startswith('Excellent!'):
                response = response.replace('!', f', {user_name}!', 1)
        return response

    def _analyze_user_intent(self, user_message: str) -> dict:
        """Analyze user intent using advanced NLP"""
        intent = {
            'primary_intent': 'inquiry',
            'business_type': None,
            'services_mentioned': [],
            'urgency_level': 'normal',
            'appointment_request': False,
            'contact_info_provided': False
        }

        msg_lower = user_message.lower()

        # Detect primary intent
        if any(word in msg_lower for word in ['schedule', 'book', 'appointment', 'meeting']):
            intent['primary_intent'] = 'appointment'
            intent['appointment_request'] = True
        elif any(word in msg_lower for word in ['price', 'cost', 'pricing', 'quote']):
            intent['primary_intent'] = 'pricing'
        elif any(word in msg_lower for word in ['help', 'need', 'want', 'looking for']):
            intent['primary_intent'] = 'service_inquiry'

        # Detect urgency
        if any(word in msg_lower for word in ['urgent', 'asap', 'immediately', 'quickly']):
            intent['urgency_level'] = 'high'

        # Detect services mentioned
        service_keywords = {
            'website': ['website', 'web', 'site', 'online'],
            'social_media': ['social', 'instagram', 'facebook', 'marketing'],
            'branding': ['logo', 'brand', 'design', 'branding'],
            'chatbot': ['chatbot', 'bot', 'automation'],
            'payment': ['payment', 'gateway', 'checkout'],
            'automation': ['automation', 'workflow', 'process']
        }

        for service, keywords in service_keywords.items():
            if any(keyword in msg_lower for keyword in keywords):
                intent['services_mentioned'].append(service)

        # Detect business type
        intent['business_type'] = self.detect_business_type(user_message)

        return intent

    def _get_business_intelligence(self, user_message: str) -> dict:
        """Get business intelligence using CSV data and LLM"""
        intelligence = {
            'business_category': None,
            'recommended_services': [],
            'similar_cases': [],
            'confidence_score': 0.0
        }

        try:
            # Use CSV data for business intelligence
            csv_match = self.find_best_match(user_message)
            if csv_match:
                intelligence['confidence_score'] = csv_match.get('score', 0.0)
                intelligence['business_category'] = csv_match.get('category', 'general')

            # Analyze service needs using existing methods
            try:
                # Use existing service detection logic
                detected_services = []
                msg_lower = user_message.lower()

                # Service detection logic
                service_keywords = {
                    'website': ['website', 'web', 'site', 'online'],
                    'social_media': ['social', 'instagram', 'facebook', 'marketing'],
                    'branding': ['logo', 'brand', 'design', 'branding'],
                    'chatbot': ['chatbot', 'bot', 'automation'],
                    'payment': ['payment', 'gateway', 'checkout'],
                    'automation': ['automation', 'workflow', 'process']
                }

                for service, keywords in service_keywords.items():
                    if any(keyword in msg_lower for keyword in keywords):
                        detected_services.append(service)

                intelligence['recommended_services'] = detected_services
            except Exception as e:
                logger.error(f"Error in service analysis: {e}")
                intelligence['recommended_services'] = []

        except Exception as e:
            logger.error(f"Error in business intelligence: {e}")

        return intelligence

    def _generate_hybrid_response(self, user_message: str, intent_analysis: dict, business_intelligence: dict) -> str:
        """Generate intelligent response using LLM + CSV hybrid approach"""
        try:
            # Check for inappropriate requests first
            if self.is_inappropriate_request(user_message):
                return "I cannot assist with illegal or inappropriate activities. I'm here to help with legitimate digital marketing and technology services."

            # Handle appointment requests intelligently
            if intent_analysis.get('appointment_request'):
                return self._handle_appointment_request(user_message, intent_analysis)

            # Try LLM response first for intelligent conversation
            if self.llm_pipeline:
                llm_response = self._get_optimized_llm_response(user_message, intent_analysis, business_intelligence)
                if llm_response and len(llm_response.strip()) > 30:
                    return llm_response

            # Fallback to enhanced CSV response
            csv_response = self._get_enhanced_csv_response(user_message, intent_analysis, business_intelligence)
            if csv_response:
                return csv_response

            # Final fallback - intelligent default response
            return self._get_intelligent_fallback_response(user_message, intent_analysis)

        except Exception as e:
            logger.error(f"Error in hybrid response generation: {e}")
            return "I apologize for the technical difficulty. How can Techrypt help your business today?"

    def _handle_appointment_request(self, user_message: str, intent_analysis: dict) -> str:
        """Handle appointment requests intelligently"""
        user_name = self.user_context.get('name', '')
        name_part = f", {user_name}" if user_name else ""

        # Check if we have user contact info
        if not self.user_context.get('email'):
            return f"""Perfect{name_part}! I'd be happy to schedule a consultation for you.

To book your appointment, I'll need:
📧 Your email address
📱 Your phone number (optional)
🏢 Your business type

Once I have these details, I can schedule your consultation with our Techrypt team. We offer 15-20 minute consultations to discuss your specific needs.

Please provide your email to get started!"""

        # If we have contact info, proceed with scheduling
        business_type = self.user_context.get('business_type', 'business')
        return f"""Excellent{name_part}! I can help you schedule a consultation with our Techrypt team.

📅 **Available Consultation Slots:**
🕘 Monday-Friday: 9:00 AM - 6:00 PM
⏱️ Duration: 15-20 minutes
💼 Focus: Your {business_type} growth needs

To complete your booking, please let me know:
📅 Your preferred date
🕐 Your preferred time
🎯 Specific services you're interested in

Would you like to schedule for this week or next week?"""

    def _get_optimized_llm_response(self, user_message: str, intent_analysis: dict, business_intelligence: dict) -> str:
        """Get optimized LLM response with context"""
        try:
            # Create intelligent prompt with context
            business_type = intent_analysis.get('business_type') or self.user_context.get('business_type', '')
            services_mentioned = intent_analysis.get('services_mentioned', [])

            # Build context-aware prompt
            context_prompt = f"""You are an intelligent business consultant AI for Techrypt.io.

USER CONTEXT:
- Business Type: {business_type or 'Not specified'}
- Services Mentioned: {', '.join(services_mentioned) if services_mentioned else 'None'}
- Intent: {intent_analysis.get('primary_intent', 'inquiry')}

TECHRYPT SERVICES:
1. Website Development - Professional, responsive websites
2. Social Media Marketing - Grow online presence
3. Branding Services - Logo and brand identity
4. Chatbot Development - Automate customer service
5. Automation Packages - Streamline business processes
6. Payment Gateway Integration - Secure online payments

INSTRUCTIONS:
- Be conversational and helpful like ChatGPT
- Map user needs to relevant Techrypt services
- Ask clarifying questions when needed
- Suggest scheduling consultation when appropriate
- Keep responses under 200 words
- Be professional but friendly

User: {user_message}
Assistant:"""

            # Generate LLM response with optimized settings
            result = self.llm_pipeline(
                context_prompt,
                max_new_tokens=150,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=50256,
                truncation=True,
                repetition_penalty=1.2
            )

            if result and len(result) > 0:
                response = result[0]['generated_text']

                # Extract assistant response
                if "Assistant:" in response:
                    response = response.split("Assistant:")[-1].strip()
                else:
                    response = response.replace(context_prompt, "").strip()

                # Clean and validate response
                response = self._clean_llm_response(response)

                if len(response) > 30 and self._is_valid_response(response):
                    return response

        except Exception as e:
            logger.error(f"LLM response error: {e}")

        return None

    def _get_enhanced_csv_response(self, user_message: str, intent_analysis: dict, business_intelligence: dict) -> str:
        """Get enhanced CSV response with intelligence"""
        try:
            # Find best CSV match
            csv_match = self.find_best_match(user_message)
            if csv_match and csv_match.get('score', 0) > 0.3:
                response = csv_match['answer']

                # Enhance with business context
                business_type = intent_analysis.get('business_type')
                if business_type and business_type not in response:
                    response = self._enhance_response_with_business_context(response, business_type)

                # Personalize with user name
                user_name = self.user_context.get('name', '')
                if user_name and user_name not in response:
                    response = self._personalize_response(response, user_message)

                return response

        except Exception as e:
            logger.error(f"Enhanced CSV response error: {e}")

        return None

    def _get_intelligent_fallback_response(self, user_message: str, intent_analysis: dict) -> str:
        """Generate intelligent fallback response"""
        user_name = self.user_context.get('name', '')
        name_part = f", {user_name}" if user_name else ""
        business_type = intent_analysis.get('business_type')

        if business_type:
            return f"""Thank you for reaching out{name_part}! For your {business_type} business, Techrypt can help you grow with our comprehensive digital services:

🚀 **Our Services:**
1. 🌐 Website Development - Professional online presence
2. 📱 Social Media Marketing - Engage your customers
3. 🎨 Branding Services - Create memorable brand identity
4. 🤖 Chatbot Development - Automate customer service
5. ⚡ Automation Packages - Streamline operations
6. 💳 Payment Gateway Integration - Secure transactions

Which service would be most valuable for your {business_type}?"""
        else:
            return f"""Hello{name_part}! I'm here to help you grow your business with Techrypt.io's digital marketing services:

🚀 **How We Can Help:**
1. 🌐 Website Development
2. 📱 Social Media Marketing
3. 🎨 Branding Services
4. 🤖 Chatbot Development
5. ⚡ Automation Packages
6. 💳 Payment Gateway Integration

What type of business do you have, and how can I assist you today?"""

    def _clean_llm_response(self, response: str) -> str:
        """Clean and validate LLM response"""
        if not response:
            return ""

        # Remove common LLM artifacts
        response = response.replace('*', '').replace('User:', '').strip()
        response = response.replace('Assistant:', '').strip()

        # Fix common issues
        response = response.replace('ypur', 'your')
        response = response.replace('will ypur', 'your')

        # Ensure Techrypt branding
        if 'techrypt' not in response.lower() and len(response) > 50:
            response += " At Techrypt, we're here to help your business grow!"

        return response[:500]  # Limit length

    def _is_valid_response(self, response: str) -> bool:
        """Validate if response is appropriate"""
        if not response or len(response) < 10:
            return False

        # Check for garbage indicators
        garbage_indicators = [
            'thanks, i\'ll edit',
            'thanks for your help',
            'very much',
            'contraception',
            'shenanigan'
        ]

        return not any(indicator in response.lower() for indicator in garbage_indicators)

    def _enhance_response_with_business_context(self, response: str, business_type: str) -> str:
        """Enhance response with business-specific context"""
        if business_type and business_type not in response:
            # Add business context naturally
            if 'for your business' in response:
                response = response.replace('for your business', f'for your {business_type} business')
            elif 'your business' in response:
                response = response.replace('your business', f'your {business_type} business')

        return response

    def _update_conversation_context(self, user_message: str, response: str):
        """Update conversation context for better follow-ups"""
        try:
            # Add to conversation history
            self.conversation_history.append({
                'user': user_message,
                'bot': response,
                'timestamp': time.time()
            })

            # Keep only last 10 exchanges for performance
            if len(self.conversation_history) > 10:
                self.conversation_history = self.conversation_history[-10:]

            # Update conversation context string
            self.conversation_context = f"Previous: User said '{user_message}', I responded about Techrypt services."

        except Exception as e:
            logger.error(f"Error updating conversation context: {e}")

    def _save_conversation_to_db(self, user_message: str, response: str):
        """Save conversation to MongoDB"""
        try:
            if self.mongodb and self.user_context.get('user_id'):
                self.mongodb.save_conversation(
                    user_id=self.user_context['user_id'],
                    user_message=user_message,
                    bot_response=response,
                    session_id=self.user_context.get('session_id')
                )
        except Exception as e:
            logger.error(f"Error saving conversation to DB: {e}")

    def is_service_request(self, user_message: str) -> bool:
        """Detect if the message is a service request to avoid CSV interference"""
        msg_lower = user_message.lower()

        # Service-related keywords that should trigger business intelligence instead of CSV
        service_indicators = [
            # Core services
            'website', 'web development', 'web design', 'site', 'online presence',
            'social media', 'instagram', 'facebook', 'social marketing', 'smm',
            'branding', 'logo', 'brand', 'design', 'graphics', 'identity',
            'chatbot', 'automation', 'bot', 'ai assistant', 'customer service',
            'payment', 'gateway', 'checkout', 'billing', 'stripe', 'paypal',

            # Service variations
            'seo', 'optimization', 'search engine', 'google ranking',
            'ecommerce', 'online store', 'shopping cart', 'e-commerce',
            'copywriting', 'content writing', 'marketing copy',
            'web scraping', 'data extraction', 'scraping services',

            # Service requests patterns
            'i need', 'help with', 'looking for', 'want to', 'how to',
            'can you help', 'do you offer', 'services for', 'assistance with',

            # Business + service combinations
            'restaurant website', 'salon social media', 'clinic appointment',
            'gym payment', 'store branding', 'business automation'
        ]

        return any(indicator in msg_lower for indicator in service_indicators)

    def clean_user_input(self, user_message: str) -> str:
        """Clean user input to prevent garbled text corruption"""
        if not user_message:
            return ""

        # Remove common garbled patterns that cause "will ypur" issues
        cleaned = user_message.replace("ypur", "your").replace("will ypur", "your")
        cleaned = cleaned.replace("chatbot, will", "chatbot and")

        # Fix common typos and garbled text
        cleaned = cleaned.replace("smmm", "smm").replace("chatbpt", "chatbot")
        cleaned = cleaned.replace("shpify", "shopify").replace("websiet", "website")
        cleaned = cleaned.replace("busines", "business").replace("servies", "services")
        cleaned = cleaned.replace("contraception", "").replace("shenanigan", "")

        # Fix more typos that real users make
        cleaned = cleaned.replace("mhy", "my").replace("teh", "the")
        cleaned = cleaned.replace("adn", "and").replace("cna", "can")
        cleaned = cleaned.replace("waht", "what").replace("hwo", "how")
        cleaned = cleaned.replace("buisiness", "business").replace("buisness", "business")

        # Fix spacing issues
        cleaned = cleaned.replace("chatbot, will", "chatbot and")
        cleaned = cleaned.replace("  ", " ")  # Remove double spaces

        # Remove extra spaces and normalize
        cleaned = " ".join(cleaned.split())

        return cleaned.strip()

    def check_and_clean_disk_space(self):
        """Check disk space and clean cache if needed"""
        try:
            import shutil
            import os

            # Check available disk space
            total, used, free = shutil.disk_usage("C:\\")
            free_gb = free // (1024**3)

            logger.info(f"💾 Available disk space: {free_gb} GB")

            # If less than 2GB free, clean Hugging Face cache
            if free_gb < 2:
                logger.warning("⚠️ Low disk space detected. Cleaning Hugging Face cache...")

                cache_dir = os.path.expanduser("~/.cache/huggingface")
                if os.path.exists(cache_dir):
                    try:
                        shutil.rmtree(cache_dir)
                        logger.info("✅ Cleaned Hugging Face cache")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not clean cache: {e}")

                # Check space again
                total, used, free = shutil.disk_usage("C:\\")
                free_gb = free // (1024**3)
                logger.info(f"💾 Available disk space after cleanup: {free_gb} GB")

        except Exception as e:
            logger.warning(f"⚠️ Could not check disk space: {e}")

    def detect_any_business_type(self, msg_lower: str) -> str:
        """UNIVERSAL business type detection - works for ANY business"""
        import re

        # Universal business patterns that work for ANY business type
        business_patterns = [
            # "I have/own/run a [business type]" patterns
            r'i have (?:a|an) (.+?)(?:\s+business|\s+company|\s+store|\s+shop|\s+service|\s+center|\s+clinic|\s+agency|\s+firm)',
            r'i own (?:a|an) (.+?)(?:\s+business|\s+company|\s+store|\s+shop|\s+service|\s+center|\s+clinic|\s+agency|\s+firm)',
            r'i run (?:a|an) (.+?)(?:\s+business|\s+company|\s+store|\s+shop|\s+service|\s+center|\s+clinic|\s+agency|\s+firm)',

            # "My [business type]" patterns
            r'my (.+?)(?:\s+business|\s+company|\s+store|\s+shop|\s+service|\s+center|\s+clinic|\s+agency|\s+firm)',
            r'help me with my (.+?)(?:\s+business|\s+company|\s+store|\s+shop|\s+service|\s+center|\s+clinic|\s+agency|\s+firm)',

            # "[business type] business" patterns
            r'(.+?)(?:\s+business|\s+company|\s+store|\s+shop|\s+service|\s+center|\s+clinic|\s+agency|\s+firm)',

            # "I work in [industry]" patterns
            r'i work in (.+?)(?:\s+industry|\s+field|\s+sector)',
            r'i work with (.+?)(?:\s+business|\s+clients|\s+customers)',

            # Direct business mentions
            r'for my (.+?)(?:\s+business|\s+company|\s+store|\s+shop)',
            r'help my (.+?)(?:\s+business|\s+company|\s+store|\s+shop)'
        ]

        # Skip common words that aren't business types
        skip_words = {
            'i', 'have', 'need', 'want', 'help', 'with', 'my', 'a', 'an', 'the', 'for', 'to', 'from',
            'business', 'company', 'store', 'shop', 'service', 'center', 'clinic', 'agency', 'firm',
            'website', 'social', 'media', 'marketing', 'logo', 'design', 'chatbot', 'automation',
            'payment', 'gateway', 'digital', 'online', 'internet', 'web', 'development'
        }

        for pattern in business_patterns:
            match = re.search(pattern, msg_lower)
            if match and match.group(1):
                business_type = match.group(1).strip()

                # Clean up the business type
                business_words = business_type.split()
                cleaned_words = [word for word in business_words if word not in skip_words and len(word) >= 3]

                if cleaned_words:
                    detected_business = ' '.join(cleaned_words)
                    # Only return if it looks like a real business type
                    if len(detected_business) >= 3 and not any(service in detected_business for service in ['website', 'social', 'marketing', 'logo']):
                        logger.info(f"🎯 Universal business detection: '{detected_business}'")
                        return detected_business

        return None

    def generate_universal_business_response(self, business_type: str, name_part: str, original_message: str) -> str:
        """Generate intelligent response for ANY business type"""

        # Analyze the original message for specific service needs
        msg_lower = original_message.lower()

        # Detect specific service mentions
        service_needs = []
        if any(word in msg_lower for word in ['website', 'web', 'site', 'online']):
            service_needs.append('website development')
        if any(word in msg_lower for word in ['social', 'instagram', 'facebook', 'marketing']):
            service_needs.append('social media marketing')
        if any(word in msg_lower for word in ['logo', 'brand', 'design']):
            service_needs.append('branding services')
        if any(word in msg_lower for word in ['chatbot', 'chat', 'automation', 'customer service']):
            service_needs.append('chatbot development')
        if any(word in msg_lower for word in ['payment', 'checkout', 'billing']):
            service_needs.append('payment gateway integration')
        if any(word in msg_lower for word in ['automation', 'workflow', 'process']):
            service_needs.append('business automation')

        # Generate intelligent business-specific recommendations
        business_recommendations = self.get_business_specific_recommendations(business_type, service_needs)

        if service_needs:
            # User mentioned specific services
            services_text = ', '.join(service_needs)
            return f"""Perfect{name_part}! For your {business_type} business, I can definitely help you with {services_text}.

{business_recommendations}

Would you like to schedule a consultation to discuss your specific {business_type} requirements and how we can help you grow your business?"""
        else:
            # General business inquiry
            return f"""Great{name_part}! For your {business_type} business, we can help you grow with our comprehensive digital marketing services:

{business_recommendations}

Would you like to schedule a consultation to discuss your specific {business_type} needs and how we can help you reach more customers?"""

    def get_business_specific_recommendations(self, business_type: str, service_needs: list) -> str:
        """Generate intelligent recommendations based on business type"""

        # Analyze business type to provide relevant recommendations
        business_lower = business_type.lower()
        recommendations = []

        # Always include core services but customize descriptions
        if 'restaurant' in business_lower or 'food' in business_lower or 'cafe' in business_lower:
            recommendations.extend([
                "🌐 **Website Development** - Online menu, ordering system, reservation booking",
                "📱 **Social Media Marketing** - Food photography, customer engagement, local promotion",
                "🎨 **Branding Services** - Menu design, logo, restaurant identity",
                "🤖 **Chatbot Development** - Order taking, reservation management, customer support",
                "💳 **Payment Gateway Integration** - Online ordering, delivery payments"
            ])
        elif 'plant' in business_lower or 'nursery' in business_lower or 'garden' in business_lower:
            recommendations.extend([
                "🌐 **Website Development** - Plant catalog, care guides, online ordering system",
                "📱 **Social Media Marketing** - Plant photography, gardening tips, seasonal promotions",
                "🎨 **Branding Services** - Nature-inspired branding, plant care materials",
                "🤖 **Chatbot Development** - Plant care advice, order management, customer support",
                "💳 **Payment Gateway Integration** - Online plant sales, delivery coordination"
            ])
        elif 'salon' in business_lower or 'beauty' in business_lower or 'spa' in business_lower:
            recommendations.extend([
                "🌐 **Website Development** - Service showcase, online booking, portfolio gallery",
                "📱 **Social Media Marketing** - Before/after photos, beauty tips, client features",
                "🎨 **Branding Services** - Elegant branding, service menus, promotional materials",
                "🤖 **Chatbot Development** - Appointment booking, service inquiries, reminders",
                "💳 **Payment Gateway Integration** - Online booking payments, service packages"
            ])
        elif 'gym' in business_lower or 'fitness' in business_lower or 'health' in business_lower:
            recommendations.extend([
                "🌐 **Website Development** - Class schedules, membership plans, trainer profiles",
                "📱 **Social Media Marketing** - Workout videos, member success stories, fitness tips",
                "🎨 **Branding Services** - Athletic branding, membership materials, signage",
                "🤖 **Chatbot Development** - Class booking, membership inquiries, workout guidance",
                "💳 **Payment Gateway Integration** - Membership billing, class payments, personal training"
            ])
        else:
            # Generic but intelligent recommendations for any business
            recommendations.extend([
                "🌐 **Website Development** - Professional website with SEO optimization",
                "📱 **Social Media Marketing** - Brand awareness and customer engagement",
                "🎨 **Branding Services** - Professional logo and brand identity",
                "🤖 **Chatbot Development** - 24/7 customer service automation",
                "⚡ **Automation Packages** - Streamline your business processes",
                "💳 **Payment Gateway Integration** - Secure online payment processing"
            ])

        return '\n'.join(recommendations)

    def generate_context_aware_platform_response(self, platform: str, business_type: str, name_part: str) -> str:
        """Generate intelligent response using conversation context"""

        # Store platform context
        self.user_context['mentioned_platform'] = platform

        # Generate business-specific platform response
        if 'bread' in business_type or 'bakery' in business_type:
            if 'shopify' in platform.lower():
                return f"""Perfect{name_part}! Setting up Shopify for your {business_type} is an excellent choice.

For your bread business on Shopify, we can help you create:
• **Custom Bakery Website** - Beautiful product galleries showcasing your breads
• **Online Ordering System** - Easy ordering with pickup/delivery options
• **Inventory Management** - Track your daily fresh bread inventory
• **Payment Integration** - Secure checkout for online orders
• **Social Media Integration** - Connect your Instagram to drive sales

Would you like to schedule a consultation to discuss your Shopify bakery store setup?"""

        elif 'egg' in business_type:
            if 'shopify' in platform.lower():
                return f"""Excellent{name_part}! Shopify is perfect for your {business_type}.

For your egg business on Shopify, we can create:
• **Fresh Egg Store** - Product pages with farm-fresh egg varieties
• **Subscription Orders** - Weekly/monthly egg delivery subscriptions
• **Local Delivery Setup** - Delivery zones and scheduling
• **Farm Story Branding** - Tell your farm's story to build trust
• **Mobile-Optimized Store** - Easy ordering on phones

Would you like to discuss how we can get your egg business online with Shopify?"""

        # Generic business + platform response
        return f"""Great{name_part}! I understand you want to use {platform.title()} for your {business_type}.

For your {business_type} on {platform.title()}, we can help with:
• **Platform Setup & Optimization** - Get the most out of {platform.title()}
• **Custom Integration** - Connect {platform.title()} with your business needs
• **Branding & Design** - Make your {platform.title()} presence stand out
• **Payment & Automation** - Streamline your {platform.title()} operations

Would you like to schedule a consultation to discuss your {platform.title()} integration strategy?"""

    def detect_multiple_services(self, msg_lower: str) -> list:
        """Detect multiple services mentioned in one request"""
        detected_services = []

        # Service detection patterns
        service_patterns = {
            'website': ['website', 'web', 'site', 'online presence', 'web development'],
            'social_media': ['social media', 'smm', 'instagram', 'facebook', 'social'],
            'branding': ['branding', 'logo', 'brand', 'design', 'identity'],
            'chatbot': ['chatbot', 'chat bot', 'bot', 'automation', 'ai'],
            'automation': ['automation', 'workflow', 'process automation'],
            'payment': ['payment', 'gateway', 'checkout', 'billing', 'stripe', 'paypal']
        }

        for service, keywords in service_patterns.items():
            if any(keyword in msg_lower for keyword in keywords):
                detected_services.append(service)

        return detected_services

    def generate_multiple_services_response(self, services: list, business_type: str, name_part: str) -> str:
        """Generate response for multiple services request"""

        # Map service names to display names
        service_display = {
            'website': '🌐 Website Development',
            'social_media': '📱 Social Media Marketing',
            'branding': '🎨 Branding Services',
            'chatbot': '🤖 Chatbot Development',
            'automation': '⚡ Automation Packages',
            'payment': '💳 Payment Gateway Integration'
        }

        # Create list of requested services
        requested_services = [service_display.get(service, service) for service in services]
        services_text = ', '.join(requested_services)

        if business_type:
            return f"""Excellent{name_part}! For your {business_type} business, I can definitely help you with all the services you mentioned:

{services_text}

This is a perfect combination for taking your business online! Our integrated approach ensures all these services work together seamlessly:

• **Website Development** - Professional online presence with SEO
• **Social Media Marketing** - Build your brand and engage customers
• **Branding Services** - Consistent visual identity across all platforms
• **Chatbot Development** - 24/7 customer service automation
• **Payment Integration** - Secure online transactions

Would you like to schedule a consultation to discuss your complete digital transformation package?"""
        else:
            return f"""Perfect{name_part}! I can help you with all the services you mentioned:

{services_text}

This is an excellent combination for building a strong online presence! Our integrated approach ensures all these services work together seamlessly.

Would you like to schedule a consultation to discuss your complete digital marketing package?"""

    def is_inappropriate_request(self, user_message: str) -> bool:
        """Check if the request is illegal, inappropriate, or harmful"""
        msg_lower = user_message.lower()

        # List of illegal/inappropriate activities
        illegal_keywords = [
            'kidnapping', 'kidnap', 'abduction', 'ransom',
            'drug', 'drugs', 'cocaine', 'heroin', 'meth',
            'weapon', 'weapons', 'gun', 'guns', 'bomb',
            'murder', 'kill', 'assassination', 'hitman',
            'fraud', 'scam', 'money laundering', 'ponzi',
            'prostitution', 'escort', 'trafficking',
            'hacking', 'hack', 'ddos', 'malware',
            'terrorism', 'terrorist', 'extremist',
            'illegal', 'criminal', 'crime'
        ]

        # Check for illegal activities
        for keyword in illegal_keywords:
            if keyword in msg_lower:
                return True

        return False

    def get_critical_hardcoded_responses(self, user_message: str) -> str:
        """Handle ONLY critical hardcoded responses - let LLM handle most queries"""
        msg_lower = user_message.lower().strip()
        user_name = self.user_context['name']
        name_part = f", {user_name}" if user_name else ""

        # ONLY handle EXACT service list requests (critical for consistency)
        exact_service_requests = ['services', 'service', 'what do you offer', 'your services', 'list services']
        if msg_lower in exact_service_requests:
            return f"""Here are our main services{name_part}:

1. Website Development
2. Social Media Marketing
3. Branding Services
4. Chatbot Development
5. Automation Packages
6. Payment Gateway Integration

Would you like to schedule a consultation to discuss your specific needs, or do you have questions about any particular service?"""

        # ONLY handle very specific service requests that need exact responses
        if msg_lower in ['web scraping', 'web scrapping', 'data scraping', 'data extraction']:
            return f"Perfect{name_part}! Our Website Development team includes web scraping and data extraction services. We can help you automate data collection from websites, extract valuable business information, and build custom scraping solutions that gather the data you need efficiently and reliably. Would you like to discuss your data extraction requirements?"

        if msg_lower in ['copywriting', 'copy writing', 'copywriter', 'copy writer']:
            return f"Perfect{name_part}! Our Branding Services team includes professional copywriting and content creation. We craft compelling marketing copy, website content, product descriptions, and sales materials that convert readers into customers. Our copywriters understand how to write persuasive content that drives results. Would you like to discuss your copywriting needs?"

        # FAST greeting responses (avoid LLM for performance)
        if msg_lower in ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'greetings']:
            return f"Hello{name_part}! Welcome to Techrypt.io. I'm here to help you grow your business with our digital marketing and technology services. What type of business do you have, or what specific service are you interested in?"

        # Let LLM handle everything else (business questions, general inquiries)
        return None

    def get_structured_response(self, user_message: str) -> str:
        """Get structured responses ONLY for very specific cases"""
        msg_lower = user_message.lower().strip()
        user_name = self.user_context['name']
        name_part = f", {user_name}" if user_name else ""

        # FIRST: Check for inappropriate/illegal requests
        if self.is_inappropriate_request(user_message):
            return f"I'm sorry{name_part}, but I cannot assist with illegal or inappropriate activities. Techrypt.io only provides legitimate digital marketing and technology services. If you have a legal business, I'd be happy to help you with our professional services."

        # ONLY handle EXACT service requests (be very specific)
        exact_service_requests = ['services', 'service', 'what do you offer', 'your services', 'list services']
        if msg_lower in exact_service_requests:
            return f"""Here are our main services{name_part}:

1. Website Development
2. Social Media Marketing
3. Branding Services
4. Chatbot Development
5. Automation Packages
6. Payment Gateway Integration

Would you like to schedule a consultation to discuss your specific needs, or do you have questions about any particular service?"""

        # ONLY handle clear business platform mentions (be more selective)
        business_response = self.detect_business_platform_strict(user_message)
        if business_response:
            return business_response

        return None

    def analyze_platform_with_llm(self, user_message: str, platform: str, name_part: str) -> str:
        """SUPER-INTELLIGENT platform analysis using web search + LLM for ANY platform"""
        try:
            print(f"🔍 Analyzing platform '{platform}' with web search + LLM...")

            # First, get comprehensive web search information about the business/platform
            search_queries = [
                f"what is {platform} business company restaurant platform",
                f"{platform} services products industry type",
                f"{platform} business model ecommerce restaurant retail"
            ]

            all_search_results = []
            for query in search_queries:
                try:
                    results = self.perform_web_search(query)
                    all_search_results.append(results)
                except:
                    continue

            # Combine all search results
            search_results = "\n\n".join(all_search_results)

            print(f"📡 Comprehensive web search completed for '{platform}'")

            # Create super-intelligent LLM prompt for business analysis
            platform_analysis_prompt = f"""
SUPER-INTELLIGENT BUSINESS ANALYSIS:
User mentioned: "{platform}"
User message: "{user_message}"

COMPREHENSIVE WEB SEARCH RESULTS ABOUT {platform.upper()}:
{search_results[:2000]}

YOUR CRITICAL TASK:
1. ANALYZE the web search results to understand what {platform} ACTUALLY IS:
   - Is it a restaurant/food business? (like Kababjis, Burger King, McDonald's)
   - Is it an e-commerce platform? (like Shopify, Amazon)
   - Is it a software/tech platform? (like Zoom, Slack)
   - Is it a retail business? (like Walmart, Target)
   - Is it a service business? (like Uber, Airbnb)
   - Is it a social media platform? (like Instagram, TikTok)

2. IDENTIFY THE BUSINESS TYPE AND INDUSTRY from web search results

3. PROVIDE INDUSTRY-SPECIFIC TECHRYPT SERVICES:
   - For RESTAURANTS: Website with online ordering, social media marketing, branding, chatbots for reservations
   - For E-COMMERCE: Website development, payment integration, social media marketing, automation
   - For RETAIL: Website, social media marketing, branding, customer service chatbots
   - For TECH PLATFORMS: Integration services, automation, custom development
   - For SERVICE BUSINESSES: Website, booking systems, social media, customer service automation

4. CREATE A BUSINESS-SPECIFIC RESPONSE (not generic platform response)

TECHRYPT SERVICES:
1. Website Development - Custom websites with SEO optimization
2. Social Media Marketing - Instagram, Facebook, LinkedIn growth
3. Branding Services - Logo design, brand identity, marketing materials
4. Chatbot Development - AI-powered customer service automation
5. Automation Packages - Business process automation solutions
6. Payment Gateway Integration - Secure payment system integration

RESPONSE REQUIREMENTS:
- Start with: "Great{name_part}! I understand you're working with {platform.title()}."
- Include a brief explanation of what {platform} is based on web search
- List 3-4 most relevant Techrypt services with SPECIFIC explanations for {platform}
- End with consultation offer
- Be platform-specific, not generic
- Use the exact platform name throughout

RESPONSE FORMAT EXAMPLES:

FOR RESTAURANTS (like Kababjis, Burger King):
"Great{name_part}! I understand you're working with {platform.title()}. Based on my research, {platform.title()} is a [restaurant/food business description from web search].

For your {platform} restaurant business, we can specifically help with:

1. 🌐 **Website Development** - Create a professional restaurant website with online ordering system, menu display, and location finder for {platform}
2. 📱 **Social Media Marketing** - Develop mouth-watering social media campaigns showcasing {platform}'s food, promotions, and customer reviews to drive foot traffic
3. 🎨 **Branding Services** - Design appetizing branding materials, menu designs, and marketing collateral that reflects {platform}'s cuisine and atmosphere
4. 🤖 **Chatbot Development** - Implement AI-powered reservation system and customer service for {platform} to handle bookings and answer menu questions 24/7

Would you like to schedule a consultation to discuss how we can grow your {platform} restaurant business?"

FOR E-COMMERCE (like Shopify stores):
"Great{name_part}! I understand you're working with {platform.title()}. Based on my research, {platform.title()} is [e-commerce description from web search].

For your {platform} e-commerce business, we can specifically help with:

1. 🌐 **Website Development** - Optimize your {platform} store with custom features, SEO, and conversion optimization
2. 💳 **Payment Gateway Integration** - Set up secure, seamless payment processing for your {platform} customers
3. 📱 **Social Media Marketing** - Drive traffic and sales to your {platform} store through targeted social campaigns
4. ⚡ **Automation Packages** - Automate {platform} inventory, order processing, and customer follow-ups

Would you like to schedule a consultation to discuss how we can boost your {platform} sales?"

FOR SERVICE BUSINESSES (like Uber, Airbnb):
"Great{name_part}! I understand you're working with {platform.title()}. Based on my research, {platform.title()} is [service business description from web search].

For your {platform} service business, we can specifically help with:

1. 🌐 **Website Development** - Build a professional service website with booking system and customer portal for {platform}
2. 🤖 **Chatbot Development** - Automate customer inquiries and booking processes for {platform} services
3. 📱 **Social Media Marketing** - Promote your {platform} services and build customer trust through social media
4. ⚡ **Automation Packages** - Streamline {platform} operations with automated scheduling and customer management

Would you like to schedule a consultation to discuss how we can optimize your {platform} service operations?"

CRITICAL REQUIREMENTS:
- ANALYZE web search to determine ACTUAL business type (restaurant, e-commerce, service, etc.)
- PROVIDE INDUSTRY-SPECIFIC services, not generic platform services
- MENTION specific features relevant to that industry (online ordering for restaurants, payment processing for e-commerce, etc.)
- Use the business type in the response (restaurant, store, service, etc.)
- If web search shows illegal/inappropriate business, decline politely
"""

            # Use simple business type detection from web search results
            business_type = self.detect_business_type_from_search(search_results, platform)

            if business_type:
                print(f"✅ Detected business type '{business_type}' for '{platform}' from web search")
                return self.generate_business_specific_response(platform, business_type, name_part)

            print(f"⚠️ Could not determine business type, using manual fallback for '{platform}'")
            # Fallback to manual platform detection
            return self.get_intelligent_platform_fallback(platform, name_part)

        except Exception as e:
            print(f"❌ Platform analysis error for '{platform}': {e}")
            return self.get_intelligent_platform_fallback(platform, name_part)

    def detect_business_type_from_search(self, search_results: str, platform: str) -> str:
        """Detect business type from web search results using improved keyword matching"""
        search_lower = search_results.lower()
        platform_lower = platform.lower()

        print(f"🔍 Analyzing search results for '{platform}': {search_results[:200]}...")

        # Enhanced business type keywords with priority scoring
        business_types = {
            'restaurant': {
                'high': ['restaurant', 'fast food', 'chain restaurant', 'food chain', 'burger', 'pizza', 'dining', 'eatery'],
                'medium': ['food', 'cuisine', 'menu', 'meal', 'eat', 'kitchen', 'chef'],
                'low': ['delivery', 'takeout', 'dine']
            },
            'ecommerce': {
                'high': ['e-commerce', 'ecommerce', 'online store', 'marketplace', 'online shopping'],
                'medium': ['selling', 'products', 'shopping', 'retail', 'store'],
                'low': ['buy', 'sell', 'purchase']
            },
            'service': {
                'high': ['ride sharing', 'transportation service', 'accommodation', 'rental service'],
                'medium': ['service', 'booking', 'appointment', 'consultation'],
                'low': ['help', 'assist', 'support']
            },
            'tech': {
                'high': ['software', 'technology company', 'tech platform', 'application'],
                'medium': ['platform', 'app', 'digital', 'tech'],
                'low': ['online', 'web']
            },
            'payment': {
                'high': ['payment processor', 'financial services', 'payment gateway'],
                'medium': ['payment', 'financial', 'money', 'transaction', 'banking'],
                'low': ['finance', 'pay']
            }
        }

        # Calculate weighted scores for each business type
        type_scores = {}
        for btype, keyword_groups in business_types.items():
            score = 0
            # High priority keywords (weight: 3)
            score += sum(3 for keyword in keyword_groups['high'] if keyword in search_lower)
            # Medium priority keywords (weight: 2)
            score += sum(2 for keyword in keyword_groups['medium'] if keyword in search_lower)
            # Low priority keywords (weight: 1)
            score += sum(1 for keyword in keyword_groups['low'] if keyword in search_lower)

            if score > 0:
                type_scores[btype] = score
                print(f"  {btype}: score {score}")

        # Special case handling for known businesses (check if platform contains these names)
        restaurant_names = ['kababjis', 'burger king', 'mcdonalds', 'pizza hut', 'kfc', 'subway', 'burger', 'pizza', 'mcdonald']
        service_names = ['uber', 'lyft', 'airbnb']
        ecommerce_names = ['amazon', 'ebay', 'etsy', 'shopify']
        payment_names = ['stripe', 'paypal', 'square']

        for name in restaurant_names:
            if name in platform_lower:
                print(f"  🍔 Detected known restaurant: {platform}")
                return 'restaurant'

        for name in service_names:
            if name in platform_lower:
                print(f"  🚗 Detected known service: {platform}")
                return 'service'

        for name in ecommerce_names:
            if name in platform_lower:
                print(f"  🛒 Detected known e-commerce: {platform}")
                return 'ecommerce'

        for name in payment_names:
            if name in platform_lower:
                print(f"  💳 Detected known payment: {platform}")
                return 'payment'

        # Return the business type with highest score (minimum score of 2 required)
        if type_scores:
            best_type = max(type_scores, key=type_scores.get)
            if type_scores[best_type] >= 2:  # Minimum confidence threshold
                print(f"  ✅ Detected business type: {best_type} (score: {type_scores[best_type]})")
                return best_type

        print(f"  ⚠️ Could not determine business type for '{platform}'")
        return None

    def generate_business_specific_response(self, platform: str, business_type: str, name_part: str) -> str:
        """Generate business-specific response based on detected type"""

        if business_type == 'restaurant':
            return f"""Great{name_part}! I understand you're working with {platform.title()}. Based on my research, {platform.title()} is a restaurant/food business.

For your {platform} restaurant business, we can help with:

1. Website Development - Restaurant website with online ordering and menu display
2. Social Media Marketing - Food photography and social campaigns to drive customers
3. Branding Services - Restaurant branding, menu design, and marketing materials
4. Chatbot Development - Reservation system and customer service automation

Would you like to schedule a consultation to discuss how we can grow your {platform} restaurant business?"""

        elif business_type == 'ecommerce':
            return f"""Great{name_part}! I understand you're working with {platform.title()}. Based on my research, {platform.title()} is an e-commerce platform.

For your {platform} e-commerce business, we can help with:

1. Website Development - E-commerce optimization and custom features
2. Payment Gateway Integration - Secure payment processing setup
3. Social Media Marketing - Drive traffic and sales through social campaigns
4. Automation Packages - Inventory management and order processing automation

Would you like to schedule a consultation to discuss how we can boost your {platform} sales?"""

        elif business_type == 'service':
            return f"""Great{name_part}! I understand you're working with {platform.title()}. Based on my research, {platform.title()} is a service business.

For your {platform} service business, we can help with:

1. Website Development - Professional service website with booking system
2. Chatbot Development - Automated customer inquiries and booking
3. Social Media Marketing - Build trust and promote your services
4. Automation Packages - Streamline operations and customer management

Would you like to schedule a consultation to discuss how we can optimize your {platform} service operations?"""

        else:
            # Generic response for other business types
            return f"""Great{name_part}! I understand you're working with {platform.title()}.

For your {platform} business, we can help with:

1. Website Development
2. Social Media Marketing
3. Branding Services
4. Chatbot Development
5. Automation Packages
6. Payment Gateway Integration

Would you like to schedule a consultation to discuss your specific {platform} requirements?"""

    def get_intelligent_platform_fallback(self, platform: str, name_part: str) -> str:
        """Intelligent fallback for platform analysis"""

        # Enhanced platform categorization
        ecommerce_platforms = [
            'shopify', 'daraz', 'amazon', 'ebay', 'etsy', 'woocommerce', 'magento', 'bigcommerce',
            'prestashop', 'opencart', 'volusion', 'ecwid', 'shopware', 'zen cart'
        ]
        social_platforms = [
            'instagram business', 'facebook ads', 'linkedin business', 'tiktok business', 'youtube channel',
            'pinterest business', 'twitter business', 'snapchat ads', 'reddit ads', 'twitch', 'discord server', 'clubhouse'
        ]
        payment_platforms = [
            'stripe', 'paypal', 'square', 'razorpay', 'klarna', 'afterpay', 'affirm',
            'wise', 'revolut', 'cashapp', 'venmo', 'zelle', 'patreon', 'gofundme'
        ]
        cms_platforms = [
            'wordpress', 'squarespace', 'wix', 'webflow', 'ghost', 'drupal', 'joomla',
            'blogger', 'medium', 'notion', 'carrd', 'strikingly', 'jimdo'
        ]
        cloud_platforms = [
            'aws', 'azure', 'google cloud', 'digitalocean', 'heroku', 'netlify', 'vercel',
            'cloudflare', 'linode', 'vultr', 'godaddy', 'bluehost', 'hostgator'
        ]
        business_platforms = [
            'salesforce', 'hubspot', 'mailchimp', 'klaviyo', 'constant contact', 'sendinblue',
            'pipedrive', 'zoho', 'freshworks', 'intercom', 'zendesk', 'drift'
        ]
        dev_platforms = [
            'github', 'gitlab', 'bitbucket', 'jira', 'trello', 'asana', 'monday',
            'slack', 'discord', 'telegram', 'zoom', 'teams', 'figma', 'canva'
        ]

        if platform in ecommerce_platforms:
            return f"""Great{name_part}! I understand you're working with {platform.title()}.

For your {platform} store, we can help with:

1. Website Development
2. Social Media Marketing
3. Branding Services
4. Chatbot Development
5. Automation Packages
6. Payment Gateway Integration

Would you like to schedule a consultation or learn more about any specific service?"""

        elif platform in social_platforms:
            return f"""Great{name_part}! I see you're working with {platform.title()}.

For your {platform} presence, we can help with:

1. Social Media Marketing
2. Branding Services
3. Chatbot Development
4. Website Development

Would you like to schedule a consultation or learn more about any specific service?"""

        elif platform in payment_platforms:
            return f"""Great{name_part}! I understand you're working with {platform.title()}.

For your {platform} integration, we can help with:

1. Payment Gateway Integration
2. Website Development
3. Chatbot Development
4. Automation Packages

Would you like to schedule a consultation or learn more about any specific service?"""

        elif platform in cms_platforms:
            return f"""Great{name_part}! I see you're using {platform.title()}.

For your {platform} website, we can help with:

1. Website Development
2. Branding Services
3. Chatbot Development
4. Automation Packages

Would you like to schedule a consultation or learn more about any specific service?"""

        elif platform in cloud_platforms:
            return f"""Great{name_part}! I see you're using {platform.title()}.

For your {platform} infrastructure, we can help with:

1. Website Development
2. Chatbot Development
3. Automation Packages
4. Branding Services

Would you like to schedule a consultation or learn more about any specific service?"""

        elif platform in business_platforms:
            return f"""Great{name_part}! I understand you're using {platform.title()}.

For your {platform} business operations, we can help with:

1. Automation Packages
2. Website Development
3. Social Media Marketing
4. Chatbot Development

Would you like to schedule a consultation or learn more about any specific service?"""

        elif platform in dev_platforms:
            return f"""Great{name_part}! I see you're using {platform.title()}.

For your {platform} workflow, we can help with:

1. Website Development
2. Chatbot Development
3. Automation Packages
4. Branding Services

Would you like to schedule a consultation or learn more about any specific service?"""

        else:
            # Generic intelligent response for unknown platforms - clean format without asterisks
            return f"""Great{name_part}! I understand you're working with {platform.title()}.

For your {platform}, we can help with:

1. Website Development
2. Social Media Marketing
3. Branding Services
4. Chatbot Development
5. Automation Packages
6. Payment Gateway Integration

Would you like to schedule a consultation to discuss your specific {platform} requirements?"""

    def get_business_intelligence_response(self, user_message: str) -> str:
        """UNIVERSAL business intelligence - handles ANY business type dynamically"""
        msg_lower = user_message.lower().strip()
        user_name = self.user_context['name']
        name_part = f", {user_name}" if user_name else ""

        # PRIORITY 1: UNIVERSAL BUSINESS TYPE DETECTION
        detected_business = self.detect_any_business_type(msg_lower)
        if detected_business:
            # Store business context for conversation memory
            self.user_context['business_type'] = detected_business
            self.user_context['services_discussed'] = []
            logger.info(f"🎯 Stored business context: {detected_business}")
            return self.generate_universal_business_response(detected_business, name_part, user_message)

        # PRIORITY 2: Handle MULTIPLE SERVICES in one request
        multiple_services = self.detect_multiple_services(msg_lower)
        if len(multiple_services) >= 2:
            return self.generate_multiple_services_response(multiple_services, detected_business, name_part)

        # PRIORITY 3: Handle MIXED SCENARIOS (business + service combinations)
        mixed_scenario = self.detect_mixed_business_service_scenario(msg_lower, name_part)
        if mixed_scenario:
            return mixed_scenario

        # PRIORITY 4: CONTEXT-AWARE PLATFORM DETECTION
        # Check if user mentions platforms in context of their known business
        mentioned_platform = self.detect_platform_enhanced(msg_lower)
        if mentioned_platform and self.user_context.get('business_type'):
            # User mentioned a platform and we know their business type
            business_type = self.user_context['business_type']
            return self.generate_context_aware_platform_response(mentioned_platform, business_type, name_part)

        # PRIORITY 4: Industry-specific business detection (fallback)
        industry_response = self.detect_industry_specific_business(msg_lower, name_part)
        if industry_response:
            return industry_response


        # If no known platform detected, check for potential unknown platforms or business types
        if not mentioned_platform:
            # Enhanced business type extraction - look for complete business phrases
            business_patterns = [
                # "I have a [business type] business" patterns
                r'i have (?:a|an) (.+?)(?:\s+business|\s+company|\s+store|\s+shop)',
                r'i own (?:a|an) (.+?)(?:\s+business|\s+company|\s+store|\s+shop)',
                r'i run (?:a|an) (.+?)(?:\s+business|\s+company|\s+store|\s+shop)',

                # "My [business type] business" patterns
                r'my (.+?)(?:\s+business|\s+company|\s+store|\s+shop)',

                # "[business type] business" patterns
                r'(.+?)(?:\s+business|\s+company|\s+store|\s+shop)',

                # Single word business types
                r'\b(wood|furniture|construction|carpentry|lumber|timber)\b'
            ]

            import re
            extracted_business = None

            for pattern in business_patterns:
                match = re.search(pattern, msg_lower)
                if match and match.group(1):
                    business_type = match.group(1).strip()

                    # Skip common words
                    skip_words = {'i', 'have', 'need', 'want', 'help', 'with', 'my', 'a', 'an', 'the', 'for', 'to', 'from'}
                    if business_type not in skip_words and len(business_type) >= 3:
                        # Always add "business" if not already present
                        if not any(word in business_type for word in ['business', 'company', 'store', 'shop', 'service']):
                            extracted_business = business_type + ' business'
                        else:
                            extracted_business = business_type
                        break

            # If we found a business type, use it as the platform
            if extracted_business:
                mentioned_platform = extracted_business
                print(f"🔍 Detected business type: '{mentioned_platform}'")
            else:
                # MUCH MORE SELECTIVE: Only look for potential platforms in very specific contexts
                # Only trigger platform detection for clear business ownership statements
                business_ownership_patterns = [
                    'i have a', 'i own a', 'i run a', 'my business is', 'i work with', 'i use'
                ]

                has_business_context = any(pattern in msg_lower for pattern in business_ownership_patterns)

                if has_business_context:
                    words = msg_lower.split()
                    potential_platforms = []

                    # Much more restrictive skip words
                    skip_words = {
                        'i', 'have', 'need', 'want', 'help', 'with', 'my', 'a', 'an', 'the', 'for', 'to', 'from',
                        'business', 'store', 'website', 'company', 'service', 'services', 'platform', 'app',
                        'hello', 'hi', 'hey', 'thanks', 'thank', 'you', 'please', 'can', 'could', 'would',
                        'what', 'how', 'when', 'where', 'why', 'who', 'which', 'do', 'does', 'did', 'will',
                        'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'and', 'or', 'but', 'so',
                        'logo', 'design', 'website', 'social', 'media', 'marketing', 'chatbot', 'automation',
                        'digital', 'tell', 'me', 'about', 'work', 'own', 'run', 'use'
                    }

                    for word in words:
                        # Only consider words that could realistically be platform names
                        if (len(word) >= 4 and  # Increased minimum length
                            word not in skip_words and
                            word.isalpha() and
                            not any(service in word for service in ['logo', 'design', 'website', 'social', 'marketing', 'digital'])):
                            potential_platforms.append(word)

                    # Only use the first potential platform if we found any
                    if potential_platforms:
                        mentioned_platform = potential_platforms[0]
                        print(f"🔍 Detected potential platform in business context: '{mentioned_platform}'")

        # Check for direct service requests BEFORE platform detection to avoid false positives
        direct_service_keywords = [
            'copywriting', 'copy writing', 'copywriter', 'copy writer', 'content writing',
            'logo design', 'logo designing', 'graphic design', 'website development', 'web design',
            'social media marketing', 'smm', 'chatbot development', 'automation services',
            'payment gateway', 'payment integration', 'branding services',
            'web scraping', 'web scrapping', 'data scraping', 'data extraction', 'web crawling',
            'scraping services', 'data mining', 'web data extraction', 'automated data collection'
        ]

        # If user mentions a direct service, handle it as a service request, not a platform
        service_detected = any(service in msg_lower for service in direct_service_keywords)

        if service_detected:
            # Continue to service detection below instead of platform analysis
            pass
        # If platform detected, use fast fallback response (disabled web search for performance)
        elif mentioned_platform:
            return self.get_intelligent_platform_fallback(mentioned_platform, name_part)

        # Comprehensive service detection keywords - covers ALL possible user inputs
        service_keywords = {
            'website': [
                'website', 'site', 'web', 'online presence', 'ecommerce', 'store', 'landing page',
                'web development', 'web design', 'online store', 'website creation', 'web app',
                'portfolio site', 'business website', 'company website', 'seo', 'search engine',
                'domain', 'hosting', 'responsive design', 'mobile site', 'web platform',
                'web scraping', 'web scrapping', 'data scraping', 'data extraction', 'web crawling',
                'scraping services', 'data mining', 'web data extraction', 'automated data collection'
            ],
            'social_media': [
                'social media', 'instagram', 'facebook', 'followers', 'engagement', 'posts', 'grow',
                'social marketing', 'smm', 'social media marketing', 'linkedin', 'twitter', 'tiktok',
                'social presence', 'social strategy', 'social campaigns',
                'influencer', 'viral', 'hashtags', 'social growth', 'social management'
            ],
            'branding': [
                'logo', 'brand', 'design', 'identity', 'graphics', 'marketing materials', 'designing',
                'logo design', 'logo designing', 'graphic design', 'branding', 'brand identity',
                'visual identity', 'corporate identity', 'brand design', 'creative design',
                'business cards', 'flyers', 'brochures', 'marketing design', 'visual branding',
                'brand strategy', 'brand development', 'creative services', 'design services',
                'copywriting', 'copy writing', 'content writing', 'copywriter', 'copy writer',
                'writing services', 'content creation', 'marketing copy', 'sales copy', 'ad copy',
                'blog writing', 'article writing', 'email copy', 'website copy', 'product descriptions'
            ],
            'chatbot': [
                'chatbot', 'automation', 'customer service', 'ai assistant', 'chat support',
                'bot development', 'conversational ai', 'virtual assistant', 'automated chat',
                'customer support automation', 'ai chatbot', 'chat automation', 'support bot'
            ],
            'automation': [
                'automation', 'workflow', 'efficiency', 'crm', 'email marketing',
                'business automation', 'workflow automation', 'marketing automation',
                'email campaigns', 'automated systems', 'business processes', 'efficiency solutions',
                'automated workflows', 'system integration', 'business optimization'
            ],
            'payment': [
                'payment', 'checkout', 'stripe', 'paypal', 'transactions', 'billing', 'gateway',
                'payment processing', 'payment integration', 'online payments', 'payment system',
                'payment gateway', 'secure payments', 'payment solutions', 'billing system',
                'transaction processing', 'payment methods', 'payment setup'
            ],
            'marketing': [
                'digital marketing', 'marketing help', 'marketing services', 'marketing strategy',
                'marketing assistance', 'marketing support', 'online marketing', 'internet marketing',
                'marketing solutions', 'marketing campaigns', 'marketing consultation', 'marketing advice'
            ]
        }

        # Exact service list requests - clean format without asterisks
        if msg_lower in ['services', 'service', 'what do you offer', 'your services', 'list services']:
            return f"""Here are Techrypt.io's main services{name_part}:

1. Website Development
2. Social Media Marketing
3. Branding Services
4. Chatbot Development
5. Automation Packages
6. Payment Gateway Integration

Would you like to schedule a consultation to discuss your specific needs with Techrypt?"""

        # Business platform detection - clean format without asterisks
        business_platforms = ['daraz', 'shopify', 'restaurant', 'salon', 'gym', 'ecommerce']
        for platform in business_platforms:
            if platform in msg_lower:
                business_type = f"{platform} business" if platform not in ['daraz', 'shopify'] else f"{platform} store"
                # Store business context for future reference
                self.user_business_context = business_type

                # Provide business-specific response
                if 'pet' in business_type.lower():
                    return f"""Perfect{name_part}! For your pet shop, Techrypt.io can help you attract more customers and grow your business with:

1. **Website Development** - Showcase your pets, services, and allow online bookings
2. **Social Media Marketing** - Share cute pet photos, build community, attract local customers
3. **Branding Services** - Create a memorable brand that pet owners trust
4. **Chatbot Development** - Answer common questions about pet care, store hours, services
5. **Automation Packages** - Automate appointment bookings, customer follow-ups
6. **Payment Gateway Integration** - Accept online payments for services and products

Pet businesses thrive on social media! Would you like to schedule a consultation to discuss how we can help your pet shop grow?"""

                elif 'restaurant' in business_type.lower() or 'food' in business_type.lower():
                    return f"""Excellent{name_part}! For your {business_type}, Techrypt.io specializes in helping food businesses attract more customers:

1. **Website Development** - Online menus, ordering system, table reservations
2. **Social Media Marketing** - Food photography, customer reviews, local promotion
3. **Branding Services** - Create an appetizing brand that draws customers
4. **Chatbot Development** - Handle reservations, menu questions, delivery inquiries
5. **Automation Packages** - Order management, customer loyalty programs
6. **Payment Gateway Integration** - Seamless online ordering and payment

Food businesses need strong online presence! Would you like to schedule a consultation to discuss your restaurant's digital growth?"""

                else:
                    return f"""Great{name_part}! For your {business_type}, Techrypt.io can help you grow with:

1. **Website Development** - Professional online presence
2. **Social Media Marketing** - Build your customer base and engagement
3. **Branding Services** - Create a memorable brand identity
4. **Chatbot Development** - Automate customer service and support
5. **Automation Packages** - Streamline your business processes
6. **Payment Gateway Integration** - Accept payments easily online

Would you like to schedule a consultation to discuss your specific business needs with Techrypt?"""

        # ENHANCED service-specific responses with comprehensive coverage
        detected_services = []
        service_scores = {}

        # Calculate service scores for better matching
        for service, keywords in service_keywords.items():
            score = sum(1 for keyword in keywords if keyword in msg_lower)
            if score > 0:
                detected_services.append(service)
                service_scores[service] = score

        if detected_services:
            # Get the highest scoring service
            primary_service = max(detected_services, key=lambda s: service_scores.get(s, 0))

            if primary_service == 'website' or 'website' in detected_services:
                # Check for specific website services
                if any(keyword in msg_lower for keyword in ['web scraping', 'web scrapping', 'data scraping', 'data extraction', 'web crawling', 'scraping services', 'data mining', 'web data extraction', 'automated data collection']):
                    return f"Perfect{name_part}! Techrypt's Website Development team includes web scraping and data extraction services. We can help you automate data collection from websites, extract valuable business information, and build custom scraping solutions that gather the data you need efficiently and reliably. Would you like to schedule a consultation to discuss your data extraction requirements with Techrypt?"
                elif any(keyword in msg_lower for keyword in ['seo', 'search engine', 'optimization', 'google', 'ranking']):
                    return f"Excellent{name_part}! Techrypt's Website Development includes comprehensive SEO optimization services. We help improve your search engine rankings, increase organic traffic, and ensure your website appears when customers search for your services. Would you like to schedule a consultation to discuss your SEO strategy with Techrypt?"
                elif any(keyword in msg_lower for keyword in ['ecommerce', 'online store', 'shopping cart', 'e-commerce']):
                    return f"Perfect{name_part}! Techrypt specializes in e-commerce website development. Our team creates powerful online stores with secure payment processing, inventory management, and user-friendly shopping experiences that drive sales. Would you like to schedule a consultation to discuss your e-commerce requirements with Techrypt?"
                else:
                    return f"I can help you with website development{name_part}! Techrypt specializes in custom website creation, SEO optimization, and e-commerce solutions. We build responsive, fast-loading sites that convert visitors into customers. Would you like to schedule a consultation to discuss your specific website needs with Techrypt?"

            elif primary_service == 'social media' or 'social_media' in detected_services:
                # Check if we know the user's business type for specific advice
                if hasattr(self, 'user_business_context') and self.user_business_context:
                    if 'pet' in self.user_business_context.lower():
                        return f"""Perfect{name_part}! Social Media Marketing is AMAZING for pet shops! Here's how Techrypt can help your pet business grow:

🐾 **Pet-Focused Social Media Strategy:**
• Share adorable pet photos and videos (pets = instant engagement!)
• Showcase happy customers with their pets
• Post pet care tips and advice (builds trust)
• Feature available pets for adoption/sale
• Share behind-the-scenes content of your shop

📈 **Results for Pet Businesses:**
• Pet content gets 3x more engagement than other industries
• Local pet owners discover your shop through hashtags
• Build a community of pet lovers who become loyal customers
• Drive foot traffic with special promotions and events

Would you like to schedule a consultation to create a winning social media strategy for your pet shop?"""

                    elif 'restaurant' in self.user_business_context.lower() or 'food' in self.user_business_context.lower():
                        return f"""Excellent{name_part}! Social Media Marketing is CRUCIAL for restaurants! Here's how Techrypt can help your food business:

🍽️ **Restaurant Social Media Strategy:**
• Mouth-watering food photography that makes people hungry
• Behind-the-scenes kitchen content
• Customer reviews and testimonials
• Daily specials and menu highlights
• Local food community engagement

📈 **Results for Restaurants:**
• Food photos drive immediate cravings and visits
• Local food lovers discover your restaurant
• Build a loyal following of regular customers
• Increase reservations and takeout orders

Would you like to schedule a consultation to create a delicious social media strategy for your restaurant?"""

                    else:
                        return f"Great{name_part}! Social Media Marketing can transform your {self.user_business_context}! Techrypt specializes in creating industry-specific strategies that drive real business results. We'll help you build a strong online presence, engage your target audience, and convert followers into customers. Would you like to schedule a consultation to discuss how social media can grow your {self.user_business_context}?"

                else:
                    # Generic response when we don't know the business type
                    if any(keyword in msg_lower for keyword in ['growth', 'strategy', 'increase', 'followers', 'engagement']):
                        return f"Great question{name_part}! Techrypt's Social Media Marketing team specializes in organic growth strategies. We create engaging content, optimize posting schedules, use targeted hashtags, and build authentic engagement to increase your followers and drive business results. Would you like to schedule a consultation to see examples of Techrypt's social media growth campaigns?"
                    else:
                        return f"Excellent{name_part}! Techrypt's Social Media Marketing team can help you build a strong online presence across Instagram, Facebook, LinkedIn, and other platforms. We create compelling content, manage your accounts, and develop strategies that convert followers into customers. Would you like to schedule a consultation to learn more about Techrypt's social media packages?"

            elif primary_service == 'branding' or 'branding' in detected_services:
                if any(keyword in msg_lower for keyword in ['copywriting', 'copy writing', 'copywriter', 'copy writer', 'content writing', 'writing services', 'marketing copy', 'sales copy']):
                    return f"Perfect{name_part}! Techrypt's Branding Services team includes professional copywriting and content creation. We craft compelling marketing copy, website content, product descriptions, and sales materials that convert readers into customers. Our copywriters understand how to write persuasive content that drives results. Would you like to schedule a consultation to discuss your copywriting needs with Techrypt?"
                elif any(keyword in msg_lower for keyword in ['logo', 'design', 'graphics', 'visual']):
                    return f"Perfect{name_part}! Techrypt's Branding Services team creates professional logos and complete brand identities. We design memorable brands that represent your business values and appeal to your target audience. Our packages include logo design, brand guidelines, and marketing materials. Would you like to schedule a consultation to see Techrypt's design portfolio?"
                else:
                    return f"Excellent{name_part}! Techrypt's Branding Services cover complete brand identity development including logo design, brand guidelines, marketing materials, and visual identity. We help businesses create memorable brands that stand out in their market. Would you like to schedule a consultation to discuss your branding vision with Techrypt?"

            elif primary_service == 'chatbot' or 'chatbot' in detected_services:
                if any(keyword in msg_lower for keyword in ['ordering', 'order', 'purchase', 'buy', 'selling']):
                    # Detect business type for specific ordering chatbot recommendations
                    business_context = ""
                    if any(word in msg_lower for word in ['egg', 'eggs']):
                        business_context = "For your egg business, our chatbot can handle customer orders, check availability, process payments, schedule deliveries, and provide product information about your eggs."
                    elif any(word in msg_lower for word in ['plant', 'plants', 'nursery']):
                        business_context = "For your plant business, our chatbot can help customers browse your plant catalog, get care instructions, place orders, schedule deliveries, and answer gardening questions."
                    elif any(word in msg_lower for word in ['food', 'restaurant', 'cafe']):
                        business_context = "For your food business, our chatbot can take orders, show menu items, handle reservations, process payments, and provide delivery updates."
                    else:
                        business_context = "Our ordering chatbot can handle customer orders, process payments, manage inventory, send confirmations, and provide order tracking."

                    return f"Excellent{name_part}! We specialize in AI-powered ordering chatbots. {business_context} Our chatbots integrate with payment systems and can handle complex order management. Would you like to discuss your specific ordering system requirements?"
                elif any(keyword in msg_lower for keyword in ['customer service', 'support', 'customer support', 'help desk']):
                    return f"Excellent{name_part}! We specialize in AI-powered customer service chatbots. Our chatbots can handle customer inquiries 24/7, provide instant responses, escalate complex issues to human agents, and significantly improve customer satisfaction while reducing support costs. Would you like to see how our chatbots can transform your customer service?"
                else:
                    return f"Perfect{name_part}! We develop intelligent chatbots for various business needs including customer service, lead generation, appointment booking, and sales support. Our AI-powered bots integrate seamlessly with your existing systems. Would you like to discuss your chatbot requirements?"

            elif primary_service == 'automation' or 'automation' in detected_services:
                if any(keyword in msg_lower for keyword in ['business process', 'workflow', 'process automation']):
                    return f"Excellent{name_part}! Our Business Process Automation services help streamline your operations, reduce manual work, and improve efficiency. We automate workflows, integrate systems, and create custom solutions that save time and reduce errors. Would you like to identify which processes we can automate for your business?"
                else:
                    return f"Great{name_part}! Our Automation Packages include email marketing automation, CRM integration, workflow optimization, and custom business process automation. We help businesses save time, reduce costs, and improve efficiency through smart automation solutions. Would you like to explore automation opportunities for your business?"

            elif primary_service == 'payment' or 'payment' in detected_services:
                return f"Perfect{name_part}! We provide comprehensive payment gateway integration services. We work with Stripe, PayPal, Square, and other major payment processors to ensure secure, seamless transactions for your customers. Our solutions include recurring billing, multi-currency support, and fraud protection. Would you like to discuss your payment processing needs?"

            elif primary_service == 'seo':
                return f"Excellent{name_part}! Our SEO optimization services help improve your search engine rankings and drive organic traffic to your website. We provide keyword research, on-page optimization, technical SEO, content strategy, and ongoing monitoring to ensure your business appears when customers search for your services. Would you like a free SEO audit?"

            elif primary_service == 'ecommerce':
                return f"Perfect{name_part}! We specialize in e-commerce development and optimization. Our team creates powerful online stores with secure payment processing, inventory management, mobile optimization, and conversion-focused design that drives sales. Would you like to discuss your online store requirements?"

            elif primary_service == 'marketing':
                return f"Excellent{name_part}! Our digital marketing services help businesses grow their online presence and reach more customers. We offer comprehensive marketing solutions including social media marketing, SEO optimization, content creation, and targeted advertising campaigns. Would you like to discuss your marketing goals and how we can help achieve them?"

        # Greeting responses
        greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'greetings']
        if any(greeting in msg_lower for greeting in greetings):
            return f"Hello{name_part}! Welcome to Techrypt.io. I'm here to help you grow your business with our digital marketing and technology services. What type of business do you have, or what specific service are you interested in?"

        # Non-business question redirection
        non_business = ['joke', 'weather', 'news', 'sports', 'entertainment']
        if any(word in msg_lower for word in non_business):
            return f"I'm here to help with business and digital marketing services{name_part}. Let me tell you about how Techrypt can help grow your business instead! We offer website development, social media marketing, branding, chatbots, automation, and payment integration. What type of business do you have?"

        return None

    def is_coherent_response(self, response: str) -> bool:
        """Check if LLM response is coherent and business-appropriate"""
        response_lower = response.lower()

        # Check for garbled text patterns
        garbled_patterns = ['contraception', 'shenanigan', 'thumbnails', 'volunteering', 'helicopricated', 'mathematize']
        if any(pattern in response_lower for pattern in garbled_patterns):
            return False

        # Check for business relevance
        business_terms = ['techrypt', 'service', 'business', 'help', 'website', 'social', 'marketing', 'brand']
        if any(term in response_lower for term in business_terms):
            return True

        # Check for reasonable length and structure
        if len(response.strip()) < 10 or len(response.split()) < 3:
            return False

        return True

    def get_contextual_response(self, user_message: str) -> str:
        """PRODUCTION-SAFE contextual response with comprehensive fallbacks"""
        try:
            # First try CSV match
            csv_match = self.find_best_match(user_message)
            if csv_match:
                response = self.personalize_csv_response(csv_match, user_message)
                if response and len(response.strip()) > 30:
                    return response

            # If no CSV match, use intelligent business detection
            business_response = self.get_business_intelligence_response(user_message)
            if business_response:
                return business_response

            # Final fallback: comprehensive default response
            user_name = self.user_context['name']
            name_part = f", {user_name}" if user_name else ""

            return f"""Thank you for your message{name_part}! I'm here to help you with Techrypt.io's digital services:

• 🌐 **Website Development** - Custom websites with SEO optimization
• 📱 **Social Media Marketing** - Instagram, Facebook, LinkedIn growth
• 🎨 **Branding Services** - Logo design, brand identity, marketing materials
• 🤖 **Chatbot Development** - AI-powered customer service automation
• ⚡ **Automation Packages** - Business process automation solutions
• 💳 **Payment Gateway Integration** - Secure payment system integration

What type of business do you have, or what specific service interests you most?"""

        except Exception as e:
            logger.error(f"Error in contextual response: {e}")
            return "Thank you for your message! I'm here to help you with Techrypt.io's digital marketing and technology services. What can I assist you with today?"

    def detect_business_platform_strict(self, user_message: str) -> str:
        """Detect business platforms with STRICT matching - only clear business statements"""
        msg_lower = user_message.lower().strip()
        user_name = self.user_context['name']
        name_part = f", {user_name}" if user_name else ""

        # Enhanced business ownership patterns (FIXED for better detection)
        business_patterns = [
            # E-commerce platforms
            'i have a daraz store', 'i have daraz store', 'my daraz store', 'daraz store',
            'i have a shopify store', 'i have shopify store', 'my shopify store', 'shopify store',
            'i have an online store', 'i have online store', 'my online store', 'online store',
            'i have an ecommerce', 'i have ecommerce', 'my ecommerce', 'ecommerce business',

            # Restaurant/Food businesses
            'i have a restaurant', 'i have restaurant', 'my restaurant', 'restaurant business',
            'i own a restaurant', 'i own restaurant', 'i run a restaurant', 'i run restaurant',
            'i have a bakery', 'i have bakery', 'my bakery', 'bakery business',
            'i own a bakery', 'i own bakery', 'i run a bakery', 'i run bakery',
            'i have a cafe', 'i have cafe', 'my cafe', 'cafe business',

            # Retail businesses
            'i have a store', 'i have store', 'my store', 'store business',
            'i have a shop', 'i have shop', 'my shop', 'shop business',
            'i run a clothing store', 'my clothing store', 'clothing store business',
            'i have a boutique', 'my boutique', 'boutique business',

            # Service businesses
            'i have a salon', 'i have salon', 'my salon', 'salon business',
            'i own a salon', 'i own salon', 'i run a salon', 'beauty salon',
            'i have a gym', 'i have gym', 'my gym', 'gym business',
            'i own a gym', 'i own gym', 'i run a gym', 'fitness center',

            # Professional services
            'i have a clinic', 'i have clinic', 'my clinic', 'clinic business',
            'i own a clinic', 'i own clinic', 'i run a clinic', 'dental clinic',
            'i have a firm', 'i have firm', 'my firm', 'firm business',
            'i own a firm', 'i own firm', 'i run a firm', 'consulting firm',
            'my construction company', 'construction company', 'construction business',
            'my photography business', 'photography business', 'photography studio'
        ]

        for pattern in business_patterns:
            if pattern in msg_lower:
                # Enhanced business type detection
                if 'daraz' in pattern:
                    business_type = 'Daraz store'
                elif 'shopify' in pattern:
                    business_type = 'Shopify store'
                elif 'online store' in pattern or 'ecommerce' in pattern:
                    business_type = 'online store'
                elif 'restaurant' in pattern:
                    business_type = 'restaurant business'
                elif 'bakery' in pattern:
                    business_type = 'bakery business'
                elif 'cafe' in pattern:
                    business_type = 'cafe business'
                elif 'clothing store' in pattern:
                    business_type = 'clothing store'
                elif 'boutique' in pattern:
                    business_type = 'boutique business'
                elif 'store' in pattern or 'shop' in pattern:
                    business_type = 'retail store'
                elif 'salon' in pattern or 'beauty salon' in pattern:
                    business_type = 'salon business'
                elif 'gym' in pattern or 'fitness center' in pattern:
                    business_type = 'gym business'
                elif 'clinic' in pattern or 'dental clinic' in pattern:
                    business_type = 'clinic business'
                elif 'firm' in pattern or 'consulting firm' in pattern:
                    business_type = 'consulting firm'
                elif 'construction' in pattern:
                    business_type = 'construction company'
                elif 'photography' in pattern:
                    business_type = 'photography business'
                else:
                    continue

                response = f"""Great{name_part}! For your {business_type}, we can help with:

1. Website Development - Custom websites with SEO optimization
2. Social Media Marketing - Instagram, Facebook, LinkedIn growth
3. Branding Services - Logo design, brand identity, marketing materials
4. Chatbot Development - AI-powered customer service automation
5. Automation Packages - Business process automation solutions
6. Payment Gateway Integration - Secure payment system integration

Would you like to schedule a consultation to discuss your specific needs?"""

                # Cache the response
                cache_key = msg_lower
                if len(self.response_cache) < 100:
                    self.response_cache[cache_key] = response
                return response

        return None

    def detect_business_platform(self, user_message: str) -> str:
        """Detect business platforms and provide structured responses"""
        msg_lower = user_message.lower()
        user_name = self.user_context['name']
        greeting = f"Great, {user_name}! " if user_name else "Great! "

        # E-commerce platforms
        if any(platform in msg_lower for platform in ['daraz', 'shopify', 'amazon', 'etsy', 'ebay', 'ecommerce', 'e-commerce', 'online store']):
            platform_name = "your e-commerce business"
            if 'daraz' in msg_lower:
                platform_name = "your Daraz store"
            elif 'shopify' in msg_lower:
                platform_name = "your Shopify store"
            elif 'amazon' in msg_lower:
                platform_name = "your Amazon business"

            return f"""{greeting}For {platform_name}, we can help with:

1. 🌐 Website Development - Custom websites with SEO optimization
2. 📱 Social Media Marketing - Instagram, Facebook, LinkedIn growth
3. 🎨 Branding Services - Logo design, brand identity, marketing materials
4. 🤖 Chatbot Development - AI-powered customer service automation
5. ⚡ Automation Packages - Business process automation solutions
6. 💳 Payment Gateway Integration - Secure payment system integration

Would you like to schedule a consultation to discuss your specific needs?"""

        # Restaurant business
        if any(word in msg_lower for word in ['restaurant', 'cafe', 'food', 'dining']):
            return f"""{greeting}For your restaurant business, we can help with:

1. 🌐 Website Development - Custom websites with SEO optimization
2. 📱 Social Media Marketing - Instagram, Facebook, LinkedIn growth
3. 🎨 Branding Services - Logo design, brand identity, marketing materials
4. 🤖 Chatbot Development - AI-powered customer service automation
5. ⚡ Automation Packages - Business process automation solutions
6. 💳 Payment Gateway Integration - Secure payment system integration

Would you like to schedule a consultation to discuss your restaurant's digital needs?"""

        # General business detection
        if any(phrase in msg_lower for phrase in ['i have', 'i own', 'i run', 'my business', 'my company']):
            return f"""{greeting}For your business, we can help with:

1. 🌐 Website Development - Custom websites with SEO optimization
2. 📱 Social Media Marketing - Instagram, Facebook, LinkedIn growth
3. 🎨 Branding Services - Logo design, brand identity, marketing materials
4. 🤖 Chatbot Development - AI-powered customer service automation
5. ⚡ Automation Packages - Business process automation solutions
6. 💳 Payment Gateway Integration - Secure payment system integration

Would you like to schedule a consultation to discuss your specific needs?"""

        return None

    def get_web_search_response(self, user_message: str) -> str:
        """Use web search for unknown services or complex questions"""
        msg_lower = user_message.lower()

        # Trigger web search for specific patterns
        search_triggers = [
            'what is', 'how to', 'explain', 'define',
            'price of', 'cost of', 'pricing for',
            'best practices', 'latest trends',
            'industry standards', 'market analysis'
        ]

        # Check if this is a service/business question that needs web search
        should_search = False

        # Check for search trigger phrases
        for trigger in search_triggers:
            if trigger in msg_lower:
                should_search = True
                break

        # Check for unknown business types or services
        unknown_business_indicators = [
            'business', 'company', 'startup', 'enterprise',
            'service', 'industry', 'market', 'platform'
        ]

        if any(indicator in msg_lower for indicator in unknown_business_indicators):
            # Check if it's not already covered by our hardcoded responses
            known_platforms = ['daraz', 'shopify', 'amazon', 'ebay', 'etsy', 'facebook', 'instagram', 'tiktok']
            if not any(platform in msg_lower for platform in known_platforms):
                should_search = True

        if should_search:
            try:
                # Use web search to get current information
                search_query = f"digital marketing services for {user_message}"
                logger.info(f"Performing web search for: {search_query}")

                # Note: In a real implementation, you would use a web search API here
                # For now, return a response indicating web search capability
                return f"I understand you're asking about '{user_message}'. Let me provide you with current information based on industry standards. For the most up-to-date and specific guidance, I recommend scheduling a consultation with our experts who can research your specific needs and provide tailored solutions. Would you like me to help you book a consultation?"

            except Exception as e:
                logger.error(f"Web search failed: {e}")
                return None

        return None

    def get_enhanced_llm_response(self, user_message: str) -> str:
        """WINDOWS-COMPATIBLE LLM response with enhanced business intelligence"""
        if not self.llm_pipeline:
            return None

        # Additional safety check at LLM level
        if self.is_inappropriate_request(user_message):
            return "I cannot assist with illegal or inappropriate activities. I'm here to help with legitimate digital marketing and technology services."

        try:
            # WINDOWS FIX: Limit input length to prevent memory issues
            if len(user_message) > 150:
                user_message = user_message[:150]

            # Create enhanced business-focused prompt
            user_name = self.user_context['name']
            name_part = f", {user_name}" if user_name else ""

            return None  # Placeholder return

        except Exception as e:
            logger.error(f"Enhanced LLM response failed: {e}")
            return None

    def get_enhanced_llm_response_with_context(self, user_message: str) -> str:
        """PERFECT LLM-CSV HYBRID: Combines LLM intelligence with CSV training for perfect alignment"""
        try:
            if not self.llm_pipeline:
                return None

            # STEP 1: Get CSV context for LLM training
            csv_context = self.get_csv_context_for_llm(user_message)

            # STEP 2: Get business intelligence context
            business_intel = self.get_business_intelligence_context(user_message)

            # STEP 3: Build comprehensive training context
            user_name = self.user_context.get('name', '')
            business_type = self.user_context.get('business_type', '')

            # Build conversation memory
            conversation_context = ""
            if hasattr(self, 'user_business_context') and self.user_business_context:
                conversation_context = f"User's Business: {self.user_business_context}"
            elif self.conversation_history:
                recent_messages = self.conversation_history[-2:]
                context_parts = []
                for msg in recent_messages:
                    if 'business' in msg.get('user', '').lower():
                        context_parts.append(f"Previous: {msg['user'][:60]}")
                if context_parts:
                    conversation_context = "; ".join(context_parts)

            # STEP 4: Create perfect training prompt with CSV examples
            training_prompt = f"""You are Techrypt.io's AI assistant trained on {len(self.training_data)} real conversations.

CSV TRAINING EXAMPLES:
{csv_context}

BUSINESS INTELLIGENCE:
{business_intel}

CONVERSATION CONTEXT:
{conversation_context}

USER CONTEXT:
- Name: {user_name if user_name else 'Not provided'}
- Business: {business_type if business_type else 'Detecting...'}

CRITICAL REQUIREMENTS:
1. ALWAYS include "Techrypt" or "Techrypt.io" in response
2. ALWAYS offer consultation/schedule discussion
3. Remember business context from conversation
4. Provide business-specific advice when possible
5. Use professional, helpful tone
6. Keep responses focused and actionable

TECHRYPT SERVICES:
1. Website Development - Custom sites, e-commerce, SEO
2. Social Media Marketing - Instagram/Facebook growth
3. Branding Services - Logo design, brand identity
4. Chatbot Development - AI customer service automation
5. Automation Packages - Business process automation
6. Payment Gateway Integration - Secure payment systems

Current User Message: "{user_message}"

Respond as Techrypt's intelligent business consultant:"""

            # STEP 5: Generate LLM response with perfect training
            response = self.llm_pipeline(
                training_prompt,
                max_length=len(training_prompt) + 250,
                num_return_sequences=1,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=50256,
                truncation=True,
                return_full_text=False
            )

            if response and len(response) > 0:
                generated_text = response[0]['generated_text'].strip()

                # STEP 6: Perfect alignment with CSV standards
                aligned_response = self.align_llm_with_csv_standards(generated_text, user_message)

                # Update business context if detected
                self.update_business_context_from_message(user_message)

                logger.info(f"✅ LLM-CSV hybrid response: {len(aligned_response)} chars")
                return aligned_response

            return None

        except Exception as e:
            logger.error(f"LLM-CSV hybrid failed: {e}")
            return None

    def update_business_context_from_message(self, message: str):
        """Update business context from user message"""
        detected_business = self.detect_any_business_type(message.lower())
        if detected_business:
            self.user_context['business_type'] = detected_business
            logger.info(f"🎯 Updated business context: {detected_business}")

    def clean_llm_response(self, response: str) -> str:
        """Clean and improve LLM response quality with GUARANTEED Techrypt branding"""
        if not response:
            return ""

        # Remove common LLM artifacts
        response = response.replace("INTELLIGENT BUSINESS RESPONSE:", "").strip()
        response = response.replace("RESPOND AS TECHRYPT'S INTELLIGENT BUSINESS CONSULTANT:", "").strip()

        # Ensure it starts properly
        if not response.startswith(("Hello", "Hi", "Great", "Perfect", "Excellent", "Thank you")):
            response = f"Thank you for your message! {response}"

        # CRITICAL: Ensure Techrypt branding is ALWAYS present
        if "techrypt" not in response.lower():
            # Force Techrypt branding into the response
            response = response.replace("we can", "Techrypt can").replace("We can", "Techrypt can")
            response = response.replace("our services", "Techrypt's services")
            response = response.replace("our team", "Techrypt's team")

            # If still no Techrypt, add it explicitly
            if "techrypt" not in response.lower():
                if "help" in response.lower():
                    response = response.replace("help", "help through Techrypt.io")
                else:
                    response = f"At Techrypt.io, {response.lower()}"

        # CRITICAL: Ensure consultation offer is ALWAYS present
        consultation_keywords = ["consultation", "schedule", "discuss", "meeting", "call"]
        if not any(keyword in response.lower() for keyword in consultation_keywords):
            response += " Would you like to schedule a consultation to discuss your specific needs with Techrypt?"

        return response

    def get_csv_context_for_llm(self, user_message: str) -> str:
        """Get relevant CSV examples to train LLM"""
        try:
            # Find similar examples from CSV data
            similar_examples = []
            user_lower = user_message.lower()

            # Get top 3 most relevant examples
            for data in self.training_data[:100]:  # Check first 100 for speed
                question = data['question'].lower()
                answer = data['answer']

                # Simple relevance scoring
                relevance_score = 0
                user_words = user_lower.split()
                for word in user_words:
                    if len(word) > 3 and word in question:
                        relevance_score += 1

                if relevance_score > 0:
                    similar_examples.append({
                        'score': relevance_score,
                        'example': f"Q: {question[:80]} A: {answer[:120]}"
                    })

            # Sort by relevance and get top 3
            similar_examples.sort(key=lambda x: x['score'], reverse=True)
            top_examples = [ex['example'] for ex in similar_examples[:3]]

            return " | ".join(top_examples) if top_examples else "No specific examples found"

        except Exception as e:
            logger.error(f"CSV context error: {e}")
            return "CSV training data available"

    def get_business_intelligence_context(self, user_message: str) -> str:
        """Get business intelligence context for LLM"""
        try:
            msg_lower = user_message.lower()

            # Detect business type
            business_type = self.detect_any_business_type(msg_lower)
            if business_type:
                context = f"Business Type Detected: {business_type}"
            else:
                context = "Business Type: Not yet identified"

            # Detect service needs
            service_keywords = {
                'website': ['website', 'site', 'web'],
                'social media': ['social', 'instagram', 'facebook', 'smm'],
                'branding': ['logo', 'brand', 'design'],
                'chatbot': ['chatbot', 'automation', 'chat'],
                'payment': ['payment', 'gateway', 'checkout'],
                'booking': ['booking', 'appointment', 'schedule']
            }

            detected_services = []
            for service, keywords in service_keywords.items():
                if any(keyword in msg_lower for keyword in keywords):
                    detected_services.append(service)

            if detected_services:
                context += f" | Services Needed: {', '.join(detected_services)}"

            return context

        except Exception as e:
            logger.error(f"Business intelligence context error: {e}")
            return "Business intelligence available"

    def align_llm_with_csv_standards(self, llm_response: str, user_message: str) -> str:
        """Align LLM response with CSV standards for perfect consistency"""
        try:
            if not llm_response:
                return ""

            # Clean the response
            response = llm_response.strip()

            # Remove common LLM artifacts
            response = response.replace("Respond as Techrypt's intelligent business consultant:", "")
            response = response.replace("As Techrypt's AI assistant,", "")
            response = response.strip()

            # Ensure proper greeting
            user_name = self.user_context.get('name', '')
            name_part = f", {user_name}" if user_name else ""

            if not response.startswith(("Hello", "Hi", "Great", "Perfect", "Excellent", "Thank you")):
                if 'cleaning' in user_message.lower():
                    response = f"Great{name_part}! For your cleaning business, {response.lower()}"
                elif any(word in user_message.lower() for word in ['restaurant', 'food', 'cafe']):
                    response = f"Perfect{name_part}! For your restaurant business, {response.lower()}"
                elif any(word in user_message.lower() for word in ['shop', 'store', 'retail']):
                    response = f"Excellent{name_part}! For your retail business, {response.lower()}"
                else:
                    response = f"Thank you{name_part}! {response}"

            # CRITICAL: Ensure Techrypt branding
            if "techrypt" not in response.lower():
                response = response.replace("we can", "Techrypt can")
                response = response.replace("our team", "Techrypt's team")
                response = response.replace("our services", "Techrypt's services")

                if "techrypt" not in response.lower():
                    if "help" in response.lower():
                        response = response.replace("help", "help through Techrypt.io")
                    else:
                        response = f"At Techrypt.io, {response.lower()}"

            # CRITICAL: Ensure consultation offer
            consultation_keywords = ["consultation", "schedule", "discuss", "meeting", "call"]
            if not any(keyword in response.lower() for keyword in consultation_keywords):
                response += " Would you like to schedule a consultation to discuss your specific needs with Techrypt?"

            # Store business context if detected
            detected_business = self.detect_any_business_type(user_message.lower())
            if detected_business:
                self.user_business_context = detected_business

            return response

        except Exception as e:
            logger.error(f"LLM alignment error: {e}")
            return llm_response

    def get_llm_response(self, user_message: str) -> str:
        """Get intelligent business-focused response that detects service needs and explains how Techrypt can help"""
        if not self.llm_pipeline:
            return None

        # Additional safety check at LLM level
        if self.is_inappropriate_request(user_message):
            return "I cannot assist with illegal or inappropriate activities. I'm here to help with legitimate digital marketing and technology services."

        try:
            # Create business-focused context for intelligent service detection
            context = f"""You are a smart business consultant AI for Techrypt.io, trained on {len(self.training_data)} real business conversations.

YOUR MISSION: Analyze user input to detect what business service they need, then explain how Techrypt can help.

BUSINESS SERVICE CATEGORIES TO DETECT:
1. Website Development - Keywords: website, site, online presence, web design, ecommerce, store, landing page
2. Social Media Marketing - Keywords: social media, Instagram, Facebook, followers, engagement, content, posts
3. Branding Services - Keywords: logo, brand, design, identity, graphics, marketing materials, business cards
4. Chatbot Development - Keywords: chatbot, automation, customer service, AI assistant, chat support
5. Automation Packages - Keywords: automation, workflow, process, efficiency, CRM, email marketing
6. Payment Gateway - Keywords: payment, checkout, stripe, paypal, transactions, billing

SPECIAL HANDLING:
- If user asks for "services" or "what do you offer" → List all 6 services in structured format
- If user mentions business platforms (Daraz, Shopify, restaurant, etc.) → Provide personalized service recommendations
- If user asks non-business questions → Redirect professionally to business services

INTELLIGENT RESPONSE STRATEGY:
1. DETECT what service category the user needs from their message
2. EXPLAIN how Techrypt specifically helps with that service
3. PROVIDE relevant examples or benefits
4. OFFER consultation booking
5. STAY BUSINESS-FOCUSED - no jokes, random chat, or off-topic responses
6. HANDLE service lists and business platforms intelligently

COMPANY CONTEXT:
{self.business_context[:300] if self.business_context else "Techrypt.io provides digital marketing services."}

RESPONSE RULES:
- Always relate responses to business services
- Detect user intent and map to our 6 service categories
- Explain specific benefits and solutions
- Use professional, helpful tone
- Offer consultation for detailed discussion
- If user asks non-business questions, redirect to business services
- Keep responses focused and actionable"""

            # Add user context
            if self.user_context['name']:
                context += f"\nUser's name: {self.user_context['name']}"

            # Create intelligent business-focused prompt
            full_prompt = f"""{context}

EXAMPLE INTELLIGENT RESPONSES:

User: "services" → "Here are our main services:
1. 🌐 Website Development - Custom websites with SEO optimization
2. 📱 Social Media Marketing - Instagram, Facebook, LinkedIn growth
3. 🎨 Branding Services - Logo design, brand identity, marketing materials
4. 🤖 Chatbot Development - AI-powered customer service automation
5. ⚡ Automation Packages - Business process automation solutions
6. 💳 Payment Gateway Integration - Secure payment system integration
Would you like to schedule a consultation to discuss your specific needs?"

User: "I have a Daraz store" → "Great! For your Daraz store, we can help with:
1. 🌐 Website Development - Custom websites with SEO optimization
2. 📱 Social Media Marketing - Instagram, Facebook, LinkedIn growth
3. 🎨 Branding Services - Logo design, brand identity, marketing materials
4. 🤖 Chatbot Development - AI-powered customer service automation
5. ⚡ Automation Packages - Business process automation solutions
6. 💳 Payment Gateway Integration - Secure payment system integration
Would you like to schedule a consultation to discuss your specific needs?"

User: "I need help with my website" → "I can help you with website development! Techrypt specializes in custom website creation, SEO optimization, and e-commerce solutions. We build responsive, fast-loading sites that convert visitors into customers. Would you like to schedule a consultation to discuss your specific website needs?"

User: "How to grow Instagram followers" → "Great question! Our Social Media Marketing team can help you grow your Instagram organically. We create engaging content strategies, optimize posting schedules, and use targeted hashtags to increase your followers and engagement. Would you like to learn more about our Instagram growth packages?"

User: "tell me a joke" → "I'm here to help with business and digital marketing services. Let me tell you about how Techrypt can help grow your business instead! We offer website development, social media marketing, branding, chatbots, automation, and payment integration. What type of business do you have?"

Now respond to this user message by detecting their service need and explaining how Techrypt can help:

User: {user_message}
Assistant:"""

            result = self.llm_pipeline(
                full_prompt,
                max_new_tokens=150,
                do_sample=True,
                temperature=0.3,  # Lower temperature for more focused responses
                pad_token_id=50256,
                truncation=True,
                repetition_penalty=1.2  # Reduce repetition
            )

            if result and len(result) > 0:
                generated_text = result[0]['generated_text']

                # Extract assistant response
                if "Assistant:" in generated_text:
                    response = generated_text.split("Assistant:")[-1].strip()
                else:
                    response = generated_text.replace(full_prompt, "").strip()

                # Clean up response
                response = response.replace('*', '').replace('User:', '').strip()
                return response[:400] if response else None

        except Exception as e:
            logger.error(f"LLM response generation error: {e}")

        return None

    def perform_web_search(self, query: str) -> str:
        """Perform lightweight web search using DuckDuckGo API"""
        try:
            import requests

            # Use DuckDuckGo Instant Answer API (free, no rate limits)
            search_url = "https://api.duckduckgo.com/"
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }

            response = requests.get(search_url, params=params, timeout=3)  # Quick timeout
            if response.status_code == 200:
                data = response.json()

                # Extract relevant information
                search_results = []

                # Get abstract (main description)
                if data.get('Abstract'):
                    search_results.append(data['Abstract'])

                # Get definition
                if data.get('Definition'):
                    search_results.append(data['Definition'])

                # Get first related topic
                if data.get('RelatedTopics') and len(data['RelatedTopics']) > 0:
                    first_topic = data['RelatedTopics'][0]
                    if isinstance(first_topic, dict) and first_topic.get('Text'):
                        search_results.append(first_topic['Text'])

                return ' '.join(search_results)[:500] if search_results else f"Information about {query}"

            return f"General information about {query}"

        except Exception as e:
            logger.error(f"Web search error: {e}")
            return f"General information about {query}"

    def get_contextual_response(self, message: str) -> str:
        """Get contextual response based on conversation history"""
        user_name = f", {self.user_context['name']}" if self.user_context['name'] else ""

        # If user has discussed services before, reference them
        if self.user_context['services_discussed']:
            return f"Thank you for your message{user_name}! Based on our conversation about {', '.join([self.services[s]['name'] for s in self.user_context['services_discussed']])}, I'm here to help you further. Could you tell me more about what you're looking for, or would you like to schedule a consultation to discuss your needs?"

        # Default response
        return f"Thank you for your message{user_name}! I'm here to help you with Techrypt.io's digital services:\n\n• 🌐 Website Development\n• 📱 Social Media Marketing\n• 🎨 Branding Services\n• 🤖 Chatbot Development\n• ⚡ Automation Packages\n• 💳 Payment Gateway Integration\n\nCould you tell me more about what you're looking for, or would you like to schedule a consultation to discuss your needs?"

    def detect_mixed_business_service_scenario(self, msg_lower: str, name_part: str) -> str:
        """ENHANCED mixed scenario detection for complex business + service combinations"""

        # EXPANDED business types with comprehensive keywords
        business_types = {
            'restaurant': ['restaurant', 'cafe', 'bakery', 'food business', 'eatery', 'diner', 'food truck', 'catering', 'pizzeria', 'bistro'],
            'store': ['store', 'shop', 'retail', 'boutique', 'outlet', 'clothing store', 'fashion store', 'apparel', 'merchandise'],
            'clinic': ['clinic', 'dental', 'medical', 'healthcare', 'doctor', 'dental clinic', 'medical practice', 'health center'],
            'salon': ['salon', 'beauty', 'spa', 'barbershop', 'beauty salon', 'hair salon', 'nail salon', 'beauty parlor'],
            'gym': ['gym', 'fitness', 'workout', 'training center', 'fitness center', 'health club', 'yoga studio'],
            'agency': ['agency', 'firm', 'company', 'consultancy', 'consulting firm', 'law firm', 'marketing agency'],
            'ecommerce': ['online store', 'ecommerce', 'e-commerce', 'online shop', 'digital store', 'web store'],
            'construction': ['construction', 'contractor', 'building', 'renovation', 'carpentry', 'plumbing', 'electrical'],
            'automotive': ['auto', 'car', 'automotive', 'garage', 'mechanic', 'car repair', 'auto shop'],
            'real_estate': ['real estate', 'property', 'realtor', 'real estate agent', 'property management'],
            'plant_nursery': ['plant', 'nursery', 'garden', 'plants', 'gardening', 'plant nursery', 'garden center', 'greenhouse', 'landscaping']
        }

        # EXPANDED service keywords with comprehensive coverage
        service_keywords = {
            'website': ['website', 'web', 'site', 'online presence', 'portfolio', 'web development', 'web design', 'landing page'],
            'social media': ['social media', 'instagram', 'facebook', 'social', 'marketing', 'social marketing', 'smm'],
            'branding': ['branding', 'logo', 'brand', 'design', 'graphics', 'identity', 'brand identity', 'visual design'],
            'chatbot': ['chatbot', 'chat', 'automation', 'bot', 'ai assistant', 'customer service', 'support'],
            'payment': ['payment', 'gateway', 'checkout', 'billing', 'payment processing', 'stripe', 'paypal'],
            'booking': ['booking', 'appointment', 'scheduling', 'reservation', 'calendar', 'appointment system'],
            'seo': ['seo', 'search engine', 'google', 'ranking', 'optimization', 'search optimization'],
            'ecommerce': ['online store', 'ecommerce', 'shopping cart', 'product catalog', 'online selling']
        }

        # ENHANCED detection with priority scoring
        detected_business = None
        business_score = 0
        for business, keywords in business_types.items():
            current_score = sum(1 for keyword in keywords if keyword in msg_lower)
            if current_score > business_score:
                business_score = current_score
                detected_business = business

        # ENHANCED service detection with priority scoring
        detected_service = None
        service_score = 0
        for service, keywords in service_keywords.items():
            current_score = sum(1 for keyword in keywords if keyword in msg_lower)
            if current_score > service_score:
                service_score = current_score
                detected_service = service

        # ENHANCED: Only proceed if we have strong confidence (score > 0)
        if detected_business and detected_service and business_score > 0 and service_score > 0:
            logger.info(f"🎯 Mixed scenario detected: {detected_business} + {detected_service} (scores: {business_score}, {service_score})")
            return self.generate_mixed_scenario_response(detected_business, detected_service, name_part)

        return None

    def generate_mixed_scenario_response(self, business_type: str, service_type: str, name_part: str) -> str:
        """Generate targeted responses for business + service combinations"""

        # Business-specific service recommendations
        responses = {
            ('restaurant', 'website'): f"""Perfect{name_part}! For your restaurant business, I can help you create a specialized website with:

🍽️ **Restaurant Website Features:**
- Online menu display with photos
- Table reservation system
- Online ordering and delivery integration
- Location and hours information
- Customer reviews section

Our Website Development team specializes in restaurant websites that drive more customers and increase orders. Would you like to discuss your restaurant website requirements?""",

            ('restaurant', 'social media'): f"""Excellent{name_part}! For your restaurant business, our Social Media Marketing can help you:

📱 **Restaurant Social Media Strategy:**
- Food photography that makes customers hungry
- Instagram and Facebook campaigns to drive foot traffic
- Customer engagement and review management
- Promotional campaigns for special offers
- Local community building

Would you like to discuss how we can grow your restaurant's social media presence?""",

            ('clinic', 'website'): f"""Perfect{name_part}! For your healthcare practice, we can create a professional medical website with:

🏥 **Medical Website Features:**
- Patient appointment booking system
- Service descriptions and doctor profiles
- Patient portal for secure communication
- Insurance and payment information
- HIPAA-compliant design

Our Website Development team understands healthcare requirements. Would you like to discuss your medical website needs?""",

            ('clinic', 'booking'): f"""Excellent{name_part}! For your healthcare practice, we can implement:

📅 **Medical Appointment System:**
- Online appointment booking for patients
- Automated appointment reminders
- Patient management system
- Integration with your existing workflow
- Mobile-friendly booking interface

Our Chatbot Development team specializes in healthcare automation. Would you like to discuss your appointment booking requirements?""",

            ('salon', 'website'): f"""Perfect{name_part}! For your beauty salon, we can create a stunning website with:

💄 **Salon Website Features:**
- Service menu with pricing
- Online appointment booking
- Before/after photo galleries
- Staff profiles and specialties
- Customer testimonials

Our Website Development team creates beautiful salon websites that attract more clients. Would you like to discuss your salon website?""",

            ('salon', 'booking'): f"""Excellent{name_part}! For your beauty salon, we can set up:

📅 **Salon Booking System:**
- Online appointment scheduling
- Service selection and staff preferences
- Automated booking confirmations
- Customer management system
- Mobile-friendly interface

Our Automation Packages are perfect for salon businesses. Would you like to discuss your booking system needs?""",

            ('gym', 'website'): f"""Perfect{name_part}! For your fitness center, we can build a dynamic website with:

💪 **Gym Website Features:**
- Membership plans and pricing
- Class schedules and trainer profiles
- Online membership signup
- Workout tips and fitness blog
- Virtual tour of facilities

Our Website Development team creates engaging fitness websites. Would you like to discuss your gym website requirements?""",

            ('agency', 'website'): f"""Perfect{name_part}! For your professional firm, we can create a sophisticated website with:

🏢 **Professional Website Features:**
- Service portfolios and case studies
- Team profiles and expertise
- Client testimonials and results
- Contact forms and consultation booking
- Professional blog and resources

Our Website Development team specializes in professional service websites. Would you like to discuss your firm's website needs?""",

            ('ecommerce', 'payment'): f"""Perfect{name_part}! For your online store, we can integrate secure payment solutions:

💳 **E-commerce Payment Integration:**
- Stripe, PayPal, and multiple payment options
- Secure checkout process
- Mobile payment optimization
- Subscription and recurring billing
- International payment support

Our Payment Gateway Integration team ensures secure, smooth transactions. Would you like to discuss your payment processing needs?""",

            ('store', 'social media'): f"""Excellent{name_part}! For your retail business, our Social Media Marketing can help you:

🛍️ **Retail Social Media Strategy:**
- Product showcases and promotions
- Customer engagement and community building
- Seasonal campaigns and sales events
- User-generated content and reviews
- Local market targeting

Would you like to discuss how we can boost your store's social media presence and drive more sales?"""
        }

        # Get specific response or generate generic one
        key = (business_type, service_type)
        if key in responses:
            return responses[key]

        # Generic mixed scenario response
        return f"""Great{name_part}! For your {business_type} business, I can definitely help you with {service_type}.

Our team specializes in providing {service_type} solutions specifically tailored for {business_type} businesses. We understand the unique needs and challenges of your industry.

Here's how we can help:
1. **Customized {service_type.title()} Solution** - Designed specifically for {business_type} businesses
2. **Industry Best Practices** - We know what works in the {business_type} industry
3. **Ongoing Support** - Continued assistance to ensure your success

Would you like to schedule a consultation to discuss your specific {service_type} requirements for your {business_type} business?"""

    def detect_platform_enhanced(self, msg_lower: str) -> str:
        """Enhanced platform detection with better recognition"""

        # Enhanced platform mapping with common variations
        platform_mapping = {
            # E-commerce Platforms
            'shopify': ['shopify'],
            'daraz': ['daraz'],
            'amazon': ['amazon'],
            'ebay': ['ebay'],
            'etsy': ['etsy'],
            'woocommerce': ['woocommerce', 'woo commerce'],

            # CMS Platforms
            'wordpress': ['wordpress', 'word press'],
            'squarespace': ['squarespace'],  # Fixed: was being confused with 'square'
            'wix': ['wix'],

            # Payment Platforms
            'stripe': ['stripe'],
            'paypal': ['paypal', 'pay pal'],
            'square': ['square payments', 'square payment'],  # More specific to avoid confusion

            # Social Media Platforms
            'instagram': ['instagram', 'instagram business'],
            'facebook': ['facebook ads', 'facebook advertising'],
            'linkedin': ['linkedin business'],

            # Marketing Platforms
            'mailchimp': ['mailchimp', 'mail chimp'],
            'klaviyo': ['klaviyo'],

            # Restaurant Chains
            'burger king': ['burger king'],
            'pizza hut': ['pizza hut'],
            'kfc': ['kfc'],
            'mcdonalds': ['mcdonalds', "mcdonald's"],
            'subway': ['subway'],
            'kababjis': ['kababjis'],
            'starbucks': ['starbucks'],
            'dominos': ['dominos', "domino's"]
        }

        # Check for platform mentions with exact matching
        for platform, variations in platform_mapping.items():
            for variation in variations:
                if variation in msg_lower:
                    return platform

        return None

    def detect_industry_specific_business(self, msg_lower: str, name_part: str) -> str:
        """Detect industry-specific business scenarios"""

        # Industry-specific keywords and responses
        industries = {
            'healthcare': {
                'keywords': ['healthcare practice', 'medical practice', 'dental clinic', 'doctor office', 'clinic'],
                'response': f"""Great{name_part}! For your healthcare practice, we can help with:

🏥 **Healthcare Digital Solutions:**
1. **Website Development** - HIPAA-compliant medical websites with patient portals
2. **Chatbot Development** - Appointment booking and patient communication systems
3. **Social Media Marketing** - Professional healthcare social media presence
4. **Automation Packages** - Patient management and appointment reminders

Would you like to discuss how we can help grow your healthcare practice?"""
            },
            'legal': {
                'keywords': ['law firm', 'legal practice', 'attorney office', 'lawyer'],
                'response': f"""Great{name_part}! For your legal practice, we can help with:

⚖️ **Legal Practice Digital Solutions:**
1. **Website Development** - Professional law firm websites with case studies
2. **Branding Services** - Professional legal branding and marketing materials
3. **Social Media Marketing** - Thought leadership and client acquisition
4. **Automation Packages** - Client management and consultation booking

Would you like to discuss how we can help grow your legal practice?"""
            },
            'real_estate': {
                'keywords': ['real estate agency', 'real estate', 'property agency', 'realtor'],
                'response': f"""Great{name_part}! For your real estate business, we can help with:

🏠 **Real Estate Digital Solutions:**
1. **Website Development** - Property listing websites with search functionality
2. **Social Media Marketing** - Property showcases and lead generation
3. **Branding Services** - Professional real estate branding
4. **Automation Packages** - Lead management and client follow-up

Would you like to discuss how we can help grow your real estate business?"""
            },
            'education': {
                'keywords': ['educational institute', 'school', 'academy', 'training center', 'education'],
                'response': f"""Great{name_part}! For your educational institution, we can help with:

🎓 **Education Digital Solutions:**
1. **Website Development** - Educational websites with course information and enrollment
2. **Social Media Marketing** - Student recruitment and engagement
3. **Chatbot Development** - Student inquiry and enrollment assistance
4. **Automation Packages** - Student management and communication

Would you like to discuss how we can help grow your educational institution?"""
            },
            'travel': {
                'keywords': ['travel agency', 'travel business', 'tour operator', 'tourism'],
                'response': f"""Great{name_part}! For your travel business, we can help with:

✈️ **Travel Business Digital Solutions:**
1. **Website Development** - Travel booking websites with itinerary management
2. **Social Media Marketing** - Destination showcases and travel inspiration
3. **Branding Services** - Travel brand identity and marketing materials
4. **Payment Gateway Integration** - Secure travel booking payments

Would you like to discuss how we can help grow your travel business?"""
            },
            'finance': {
                'keywords': ['financial services', 'financial advisor', 'accounting firm', 'finance'],
                'response': f"""Great{name_part}! For your financial services business, we can help with:

💰 **Financial Services Digital Solutions:**
1. **Website Development** - Professional financial websites with client portals
2. **Branding Services** - Trustworthy financial brand identity
3. **Social Media Marketing** - Financial education and client acquisition
4. **Automation Packages** - Client management and appointment scheduling

Would you like to discuss how we can help grow your financial services business?"""
            }
        }

        # Check for industry matches
        for industry, data in industries.items():
            if any(keyword in msg_lower for keyword in data['keywords']):
                return data['response']

        return None

    def reset_context(self):
        """Reset conversation context for new sessions"""
        self.conversation_history = []
        self.user_context = {
            'name': '',
            'business_type': '',
            'interests': [],
            'previous_topics': [],
            'services_discussed': [],
            'session_id': '',
            'conversation_count': 0
        }
        logger.info("AI context reset for new session")

# Global AI instance
techrypt_ai = TechryptAI()

def get_ai_response(user_message: str, user_name: str = '') -> str:
    """Main function to get AI response"""
    try:
        response = techrypt_ai.generate_intelligent_response(user_message, user_name)
        techrypt_ai.add_to_history(user_message, response)
        return response
    except Exception as e:
        logger.error(f"Error generating AI response: {e}")
        return "I apologize, but I'm having trouble processing your request. Please try again or contact our support team."

if __name__ == "__main__":
    # Test the AI
    print("Testing Techrypt AI...")

    test_messages = [
        "Hello",
        "I have an ecommerce store",
        "teespring",
        "I want design for t-shirt",
        "what have I asked you before?"
    ]

    for msg in test_messages:
        response = get_ai_response(msg, "Muhammad Mudassir")
        print(f"\nUser: {msg}")
        print(f"AI: {response}")
