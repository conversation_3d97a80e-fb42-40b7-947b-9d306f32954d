#!/usr/bin/env python3
"""
Direct test of the AI backend to isolate issues
"""

import sys
import os
sys.path.append('.')

def test_ai_backend_directly():
    """Test the AI backend directly"""
    print("🧪 TESTING AI BACKEND DIRECTLY")
    print("=" * 50)
    
    try:
        # Import the AI backend
        from Techrypt_sourcecode.Techrypt.src.ai_backend import get_ai_response, techrypt_ai
        print("✅ AI backend imported successfully")
        
        # Test 1: Simple hello
        print("\n1. Testing 'hello'...")
        try:
            response = get_ai_response("hello", "")
            print(f"✅ Response: {response[:100]}...")
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 2: Business query
        print("\n2. Testing 'I have a restaurant'...")
        try:
            response = get_ai_response("I have a restaurant", "John")
            print(f"✅ Response: {response[:100]}...")
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 3: Direct method call
        print("\n3. Testing direct method call...")
        try:
            response = techrypt_ai.generate_intelligent_response("hello", "")
            print(f"✅ Direct response: {response[:100]}...")
        except Exception as e:
            print(f"❌ Direct error: {e}")
            import traceback
            traceback.print_exc()
            
        # Test 4: Check LLM pipeline
        print("\n4. Checking LLM pipeline...")
        try:
            if hasattr(techrypt_ai, 'llm_pipeline') and techrypt_ai.llm_pipeline:
                print("✅ LLM pipeline is available")
            else:
                print("⚠️ LLM pipeline is not available")
        except Exception as e:
            print(f"❌ LLM check error: {e}")
            
        # Test 5: Check training data
        print("\n5. Checking training data...")
        try:
            if hasattr(techrypt_ai, 'training_data') and techrypt_ai.training_data:
                print(f"✅ Training data available: {len(techrypt_ai.training_data)} entries")
            else:
                print("⚠️ No training data available")
        except Exception as e:
            print(f"❌ Training data check error: {e}")
            
    except Exception as e:
        print(f"❌ Failed to import AI backend: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ai_backend_directly()
